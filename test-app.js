/**
 * 测试应用启动脚本
 * 
 * 简化版本的应用启动，用于调试
 */

require('dotenv').config();

console.log('🔍 开始测试应用启动...');

try {
  console.log('1. 加载环境变量...');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('PORT:', process.env.PORT);
  
  console.log('2. 加载Express应用...');
  const app = require('./src/app');
  console.log('✅ Express应用加载成功');
  
  console.log('3. 启动服务器...');
  const PORT = process.env.PORT || 3000;
  
  const server = app.listen(PORT, () => {
    console.log(`✅ 服务器启动成功，端口: ${PORT}`);
    console.log(`🌐 访问地址: http://localhost:${PORT}`);
    
    // 测试完成后关闭服务器
    setTimeout(() => {
      console.log('🛑 测试完成，关闭服务器...');
      server.close();
      process.exit(0);
    }, 2000);
  });
  
} catch (error) {
  console.error('❌ 应用启动失败:', error);
  console.error('错误堆栈:', error.stack);
  process.exit(1);
}
