/**
 * 认证中间件 (Authentication Middleware)
 * 
 * 验证JWT令牌并保护需要认证的路由
 */

const { verifyAccessToken } = require('../config/jwt');
const { User } = require('../models');
const { errorResponse } = require('../utils/response');
const { asyncHandler, AppError } = require('./error.middleware');

/**
 * 保护路由中间件 - 验证JWT令牌
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
const protect = asyncHandler(async (req, res, next) => {
  let token;

  // 从请求头获取令牌
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // 检查令牌是否存在
  if (!token) {
    throw new AppError('访问被拒绝，请提供有效的访问令牌', 401, 'NO_TOKEN_PROVIDED');
  }

  try {
    // 验证令牌
    const decoded = verifyAccessToken(token);

    // 查找用户
    const user = await User.findById(decoded.userId);

    if (!user) {
      throw new AppError('令牌无效，用户不存在', 401, 'INVALID_TOKEN_USER_NOT_FOUND');
    }

    // 检查用户是否激活
    if (!user.isActive) {
      throw new AppError('账户已被禁用', 403, 'ACCOUNT_DISABLED');
    }

    // 将用户信息添加到请求对象
    req.user = {
      userId: user._id,
      email: user.email,
      username: user.username,
      role: user.role,
      isActive: user.isActive
    };

    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
    } else if (error.name === 'TokenExpiredError') {
      throw new AppError('访问令牌已过期', 401, 'TOKEN_EXPIRED');
    } else {
      throw error;
    }
  }
});

/**
 * 可选认证中间件 - 如果有令牌则验证，没有则继续
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  // 从请求头获取令牌
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // 如果没有令牌，直接继续
  if (!token) {
    return next();
  }

  try {
    // 验证令牌
    const decoded = verifyAccessToken(token);

    // 查找用户
    const user = await User.findById(decoded.userId);

    if (user && user.isActive) {
      // 将用户信息添加到请求对象
      req.user = {
        userId: user._id,
        email: user.email,
        username: user.username,
        role: user.role,
        isActive: user.isActive
      };
    }

    next();

  } catch (error) {
    // 令牌无效时不抛出错误，继续执行
    next();
  }
});

module.exports = {
  protect,
  optionalAuth
};
