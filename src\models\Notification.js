/**
 * 通知模型 (Notification Model)
 * 
 * 定义通知消息数据结构和相关方法
 */

const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必需的']
  },
  
  type: {
    type: String,
    enum: {
      values: ['due_reminder', 'overdue', 'return_success', 'system', 'fine_notice'],
      message: '通知类型必须是 due_reminder, overdue, return_success, system 或 fine_notice'
    },
    required: [true, '通知类型是必需的']
  },
  
  title: {
    type: String,
    required: [true, '通知标题是必需的'],
    trim: true,
    maxlength: [100, '通知标题不能超过100个字符']
  },
  
  message: {
    type: String,
    required: [true, '通知内容是必需的'],
    trim: true,
    maxlength: [500, '通知内容不能超过500个字符']
  },
  
  isRead: {
    type: Boolean,
    default: false
  },
  
  relatedRecord: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BorrowingRecord',
    default: null
  },
  
  readAt: {
    type: Date
  },
  
  // 通知优先级
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
notificationSchema.index({ user: 1, isRead: 1 });
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ priority: 1 });

// 保存前中间件：设置阅读时间
notificationSchema.pre('save', function(next) {
  if (this.isModified('isRead') && this.isRead && !this.readAt) {
    this.readAt = new Date();
  }
  next();
});

// 实例方法：标记为已读
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

// 静态方法：创建通知
notificationSchema.statics.createNotification = function(data) {
  return this.create(data);
};

// 静态方法：获取用户未读通知数量
notificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({ user: userId, isRead: false });
};

// 静态方法：批量标记为已读
notificationSchema.statics.markAllAsRead = function(userId) {
  return this.updateMany(
    { user: userId, isRead: false },
    { isRead: true, readAt: new Date() }
  );
};

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
