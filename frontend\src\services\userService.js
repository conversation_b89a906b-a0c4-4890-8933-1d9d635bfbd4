/**
 * 用户服务
 * 
 * 处理用户相关的API请求
 */

import api from './api';

export const userService = {
  // 获取所有用户（管理员）
  getAllUsers: async (params = {}) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  // 获取用户详情
  getUserById: async (userId) => {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },

  // 更新用户状态
  updateUserStatus: async (userId, isActive) => {
    const response = await api.patch(`/users/${userId}/status`, { isActive });
    return response.data;
  },

  // 更新用户角色
  updateUserRole: async (userId, role) => {
    const response = await api.patch(`/users/${userId}/role`, { role });
    return response.data;
  },

  // 获取用户统计
  getUserStats: async () => {
    const response = await api.get('/users/stats');
    return response.data;
  },

  // 获取用户借阅统计
  getUserBorrowingStats: async (userId) => {
    const response = await api.get(`/users/${userId}/borrowing-stats`);
    return response.data;
  },

  // 更新用户信息
  updateUserProfile: async (userId, profileData) => {
    const response = await api.put(`/users/${userId}/profile`, profileData);
    return response.data;
  },

  // 修改密码
  changePassword: async (userId, passwordData) => {
    const response = await api.patch(`/users/${userId}/password`, passwordData);
    return response.data;
  },

  // 删除用户（管理员）
  deleteUser: async (userId) => {
    const response = await api.delete(`/users/${userId}`);
    return response.data;
  },

  // 重置用户密码（管理员）
  resetUserPassword: async (userId) => {
    const response = await api.post(`/users/${userId}/reset-password`);
    return response.data;
  },
};
