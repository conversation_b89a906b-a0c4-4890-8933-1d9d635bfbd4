/**
 * 图书控制器 (Book Controller)
 * 
 * 处理图书的增删改查操作
 */

const { Book, Category } = require('../models');
const { successResponse, paginatedResponse, errorResponse } = require('../utils/response');
const { asyncHandler, AppError } = require('../middlewares/error.middleware');
const logger = require('../utils/logger');

/**
 * 创建新图书
 * @route POST /api/books
 * @access Private (Admin only)
 */
const createBook = asyncHandler(async (req, res) => {
  const {
    title,
    author,
    isbn,
    description,
    category,
    publisher,
    publishedYear,
    language,
    pages,
    stock,
    totalCopies,
    coverImage,
    tags
  } = req.body;

  // 检查必填字段
  if (!title || !author || !category || !totalCopies) {
    throw new AppError('标题、作者、分类和总册数是必需的', 400, 'MISSING_REQUIRED_FIELDS');
  }

  // 检查ISBN是否已存在（如果提供了ISBN）
  if (isbn) {
    const existingBook = await Book.findOne({ isbn });
    if (existingBook) {
      throw new AppError('该ISBN已存在', 409, 'ISBN_ALREADY_EXISTS');
    }
  }

  // 创建图书数据
  const bookData = {
    title,
    author,
    isbn,
    description,
    category,
    publisher,
    publishedYear,
    language,
    pages,
    stock: stock || totalCopies, // 如果没有指定库存，默认等于总册数
    totalCopies,
    coverImage,
    tags: tags || [],
    createdBy: req.user.userId
  };

  // 创建图书
  const book = await Book.create(bookData);

  // 记录日志
  logger.info(`新图书创建: ${book.title} by ${req.user.username}`);

  res.status(201).json(successResponse(book, '图书创建成功'));
});

/**
 * 获取图书列表
 * @route GET /api/books
 * @access Private (All authenticated users)
 */
const getBooks = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    category,
    status = 'available',
    sortBy = 'createdAt',
    sortOrder = 'desc',
    author,
    language
  } = req.query;

  // 构建查询条件
  const query = {};

  // 状态筛选
  if (status !== 'all') {
    query.status = status;
  }

  // 分类筛选
  if (category && category !== 'all') {
    query.category = category;
  }

  // 作者筛选
  if (author) {
    query.author = new RegExp(author, 'i');
  }

  // 语言筛选
  if (language && language !== 'all') {
    query.language = language;
  }

  // 文本搜索
  if (search) {
    query.$or = [
      { title: new RegExp(search, 'i') },
      { author: new RegExp(search, 'i') },
      { description: new RegExp(search, 'i') },
      { tags: { $in: [new RegExp(search, 'i')] } }
    ];
  }

  // 排序设置
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // 分页设置
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  // 执行查询
  const [books, total] = await Promise.all([
    Book.find(query)
      .populate('createdBy', 'username email')
      .sort(sort)
      .skip(skip)
      .limit(limitNum),
    Book.countDocuments(query)
  ]);

  // 返回分页结果
  res.json(paginatedResponse(books, {
    page: pageNum,
    limit: limitNum,
    total
  }, '获取图书列表成功'));
});

/**
 * 获取单本图书详情
 * @route GET /api/books/:id
 * @access Private (All authenticated users)
 */
const getBookById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const book = await Book.findById(id)
    .populate('createdBy', 'username email profile');

  if (!book) {
    throw new AppError('图书不存在', 404, 'BOOK_NOT_FOUND');
  }

  res.json(successResponse(book, '获取图书详情成功'));
});

/**
 * 更新图书信息
 * @route PUT /api/books/:id
 * @access Private (Admin only)
 */
const updateBook = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  // 查找图书
  const book = await Book.findById(id);

  if (!book) {
    throw new AppError('图书不存在', 404, 'BOOK_NOT_FOUND');
  }

  // 如果更新ISBN，检查是否与其他图书冲突
  if (updateData.isbn && updateData.isbn !== book.isbn) {
    const existingBook = await Book.findOne({ isbn: updateData.isbn, _id: { $ne: id } });
    if (existingBook) {
      throw new AppError('该ISBN已被其他图书使用', 409, 'ISBN_ALREADY_EXISTS');
    }
  }

  // 验证库存不能超过总册数
  if (updateData.stock !== undefined && updateData.totalCopies !== undefined) {
    if (updateData.stock > updateData.totalCopies) {
      throw new AppError('库存数量不能超过总册数', 400, 'INVALID_STOCK_COUNT');
    }
  } else if (updateData.stock !== undefined && updateData.stock > book.totalCopies) {
    throw new AppError('库存数量不能超过总册数', 400, 'INVALID_STOCK_COUNT');
  } else if (updateData.totalCopies !== undefined && book.stock > updateData.totalCopies) {
    throw new AppError('总册数不能少于当前库存数量', 400, 'INVALID_TOTAL_COPIES');
  }

  // 更新图书
  const updatedBook = await Book.findByIdAndUpdate(
    id,
    { ...updateData, updatedAt: new Date() },
    { new: true, runValidators: true }
  ).populate('createdBy', 'username email');

  // 记录日志
  logger.info(`图书更新: ${updatedBook.title} by ${req.user.username}`);

  res.json(successResponse(updatedBook, '图书更新成功'));
});

/**
 * 删除图书
 * @route DELETE /api/books/:id
 * @access Private (Admin only)
 */
const deleteBook = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const book = await Book.findById(id);

  if (!book) {
    throw new AppError('图书不存在', 404, 'BOOK_NOT_FOUND');
  }

  // 检查是否有活跃的借阅记录
  const { BorrowingRecord } = require('../models');
  const activeBorrowings = await BorrowingRecord.countDocuments({
    book: id,
    status: 'active'
  });

  if (activeBorrowings > 0) {
    throw new AppError('该图书有活跃的借阅记录，无法删除', 400, 'BOOK_HAS_ACTIVE_BORROWINGS');
  }

  // 软删除：将状态设置为retired而不是真正删除
  await Book.findByIdAndUpdate(id, { 
    status: 'retired',
    updatedAt: new Date()
  });

  // 记录日志
  logger.info(`图书删除: ${book.title} by ${req.user.username}`);

  res.json(successResponse(null, '图书删除成功'));
});

/**
 * 更新图书库存
 * @route PATCH /api/books/:id/stock
 * @access Private (Admin only)
 */
const updateBookStock = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { stock } = req.body;

  if (stock === undefined || stock < 0) {
    throw new AppError('库存数量必须是非负数', 400, 'INVALID_STOCK');
  }

  const book = await Book.findById(id);

  if (!book) {
    throw new AppError('图书不存在', 404, 'BOOK_NOT_FOUND');
  }

  if (stock > book.totalCopies) {
    throw new AppError('库存数量不能超过总册数', 400, 'STOCK_EXCEEDS_TOTAL');
  }

  // 更新库存
  book.stock = stock;
  await book.save();

  // 记录日志
  logger.info(`图书库存更新: ${book.title} 库存: ${stock} by ${req.user.username}`);

  res.json(successResponse(book, '库存更新成功'));
});

/**
 * 获取图书统计信息
 * @route GET /api/books/stats
 * @access Private (Admin only)
 */
const getBookStats = asyncHandler(async (req, res) => {
  const stats = await Book.aggregate([
    {
      $group: {
        _id: null,
        totalBooks: { $sum: 1 },
        availableBooks: {
          $sum: { $cond: [{ $eq: ['$status', 'available'] }, 1, 0] }
        },
        borrowedBooks: {
          $sum: { $cond: [{ $eq: ['$status', 'borrowed'] }, 1, 0] }
        },
        totalCopies: { $sum: '$totalCopies' },
        totalStock: { $sum: '$stock' }
      }
    }
  ]);

  const categoryStats = await Book.getCategoryStats();

  res.json(successResponse({
    overview: stats[0] || {
      totalBooks: 0,
      availableBooks: 0,
      borrowedBooks: 0,
      totalCopies: 0,
      totalStock: 0
    },
    categoryStats
  }, '获取图书统计成功'));
});

module.exports = {
  createBook,
  getBooks,
  getBookById,
  updateBook,
  deleteBook,
  updateBookStock,
  getBookStats
};
