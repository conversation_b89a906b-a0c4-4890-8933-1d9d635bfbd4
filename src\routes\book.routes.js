/**
 * 图书路由 (Book Routes)
 * 
 * 定义图书管理相关的API端点
 */

const express = require('express');
const { body, query, param } = require('express-validator');
const {
  createBook,
  getBooks,
  getBookById,
  updateBook,
  deleteBook,
  updateBookStock,
  getBookStats
} = require('../controllers/book.controller');
const { protect } = require('../middlewares/auth.middleware');
const { adminOnly, memberAndAbove } = require('../middlewares/role.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

// 图书创建验证规则
const createBookValidation = [
  body('title')
    .trim()
    .notEmpty()
    .withMessage('图书标题是必需的')
    .isLength({ max: 200 })
    .withMessage('图书标题不能超过200个字符'),
  
  body('author')
    .trim()
    .notEmpty()
    .withMessage('作者是必需的')
    .isLength({ max: 100 })
    .withMessage('作者名称不能超过100个字符'),
  
  body('isbn')
    .optional()
    .trim()
    .matches(/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/)
    .withMessage('请输入有效的ISBN号码'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('图书描述不能超过1000个字符'),
  
  body('category')
    .trim()
    .notEmpty()
    .withMessage('图书分类是必需的')
    .isLength({ max: 50 })
    .withMessage('分类名称不能超过50个字符'),
  
  body('publisher')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('出版社名称不能超过100个字符'),
  
  body('publishedYear')
    .optional()
    .isInt({ min: 1000, max: new Date().getFullYear() + 1 })
    .withMessage('出版年份必须是有效的年份'),
  
  body('language')
    .optional()
    .trim()
    .isLength({ max: 30 })
    .withMessage('语言名称不能超过30个字符'),
  
  body('pages')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页数必须是正整数'),
  
  body('stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('库存数量必须是非负整数'),
  
  body('totalCopies')
    .isInt({ min: 1 })
    .withMessage('总册数必须是正整数'),
  
  body('coverImage')
    .optional()
    .isURL()
    .withMessage('封面图片必须是有效的URL'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式'),
  
  body('tags.*')
    .optional()
    .trim()
    .isLength({ max: 30 })
    .withMessage('标签长度不能超过30个字符')
];

// 图书更新验证规则（与创建类似，但所有字段都是可选的）
const updateBookValidation = [
  body('title')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('图书标题不能为空')
    .isLength({ max: 200 })
    .withMessage('图书标题不能超过200个字符'),
  
  body('author')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('作者不能为空')
    .isLength({ max: 100 })
    .withMessage('作者名称不能超过100个字符'),
  
  body('isbn')
    .optional()
    .trim()
    .matches(/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/)
    .withMessage('请输入有效的ISBN号码'),
  
  body('stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('库存数量必须是非负整数'),
  
  body('totalCopies')
    .optional()
    .isInt({ min: 1 })
    .withMessage('总册数必须是正整数')
];

// 库存更新验证规则
const stockUpdateValidation = [
  body('stock')
    .isInt({ min: 0 })
    .withMessage('库存数量必须是非负整数')
];

// 查询参数验证规则
const getBooksValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  
  query('sortBy')
    .optional()
    .isIn(['title', 'author', 'createdAt', 'updatedAt', 'publishedYear', 'stock', 'borrowingStats.totalBorrowed'])
    .withMessage('排序字段无效'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序顺序必须是asc或desc'),
  
  query('status')
    .optional()
    .isIn(['available', 'borrowed', 'maintenance', 'retired', 'all'])
    .withMessage('状态参数无效')
];

// ID参数验证
const idValidation = [
  param('id')
    .isMongoId()
    .withMessage('无效的图书ID')
];

// 公开路由（需要认证但不需要特定角色）
router.get('/', protect, memberAndAbove, getBooksValidation, validate, getBooks);
router.get('/stats', protect, adminOnly, getBookStats);
router.get('/:id', protect, memberAndAbove, idValidation, validate, getBookById);

// 管理员专用路由
router.post('/', protect, adminOnly, createBookValidation, validate, createBook);
router.put('/:id', protect, adminOnly, idValidation, updateBookValidation, validate, updateBook);
router.delete('/:id', protect, adminOnly, idValidation, validate, deleteBook);
router.patch('/:id/stock', protect, adminOnly, idValidation, stockUpdateValidation, validate, updateBookStock);

module.exports = router;
