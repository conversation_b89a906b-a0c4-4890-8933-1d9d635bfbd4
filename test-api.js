/**
 * API测试脚本
 * 
 * 测试后端API是否正常工作
 */

const http = require('http');

function testAPI(path, expectedStatus = 200) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            data: data ? JSON.parse(data) : null,
            headers: res.headers
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 开始API测试...\n');

  const tests = [
    { path: '/health', name: '健康检查' },
    { path: '/', name: '根路径' },
    { path: '/api', name: 'API根路径' },
  ];

  for (const test of tests) {
    try {
      console.log(`测试: ${test.name} (${test.path})`);
      const result = await testAPI(test.path);
      
      if (result.status === 200) {
        console.log(`✅ 成功 - 状态码: ${result.status}`);
        if (result.data) {
          console.log(`   响应: ${JSON.stringify(result.data, null, 2)}`);
        }
      } else {
        console.log(`⚠️  状态码: ${result.status}`);
        console.log(`   响应: ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ 失败: ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 API测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = runTests;
