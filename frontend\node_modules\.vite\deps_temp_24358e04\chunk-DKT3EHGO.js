import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js
function _setPrototypeOf(t, e) {
  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t2, e2) {
    return t2.__proto__ = e2, t2;
  }, _setPrototypeOf(t, e);
}
var init_setPrototypeOf = __esm({
  "node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js"() {
  }
});

export {
  _setPrototypeOf,
  init_setPrototypeOf
};
//# sourceMappingURL=chunk-DKT3EHGO.js.map
