/**
 * 角色权限中间件 (Role Authorization Middleware)
 * 
 * 基于用户角色进行访问控制
 */

const { errorResponse } = require('../utils/response');
const { AppError } = require('./error.middleware');

/**
 * 角色授权中间件
 * @param {...string} allowedRoles - 允许访问的角色列表
 * @returns {Function} Express中间件函数
 * 
 * @example
 * // 只允许管理员访问
 * router.get('/admin-only', protect, authorize('admin'), controller);
 * 
 * // 允许管理员和会员访问
 * router.get('/members', protect, authorize('admin', 'member'), controller);
 */
const authorize = (...allowedRoles) => {
  return (req, res, next) => {
    // 检查用户是否已通过认证
    if (!req.user) {
      return res.status(401).json(errorResponse(
        'AUTHENTICATION_REQUIRED',
        '请先登录后再访问此资源',
        401
      ));
    }

    // 检查用户角色是否在允许的角色列表中
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json(errorResponse(
        'INSUFFICIENT_PERMISSIONS',
        `访问被拒绝。需要以下角色之一: ${allowedRoles.join(', ')}`,
        403
      ));
    }

    // 角色验证通过，继续执行
    next();
  };
};

/**
 * 管理员专用中间件
 * 只允许管理员角色访问
 */
const adminOnly = authorize('admin');

/**
 * 会员及以上权限中间件
 * 允许会员和管理员访问
 */
const memberAndAbove = authorize('member', 'admin');

/**
 * 资源所有者或管理员中间件
 * 允许资源所有者或管理员访问
 * @param {string} resourceUserField - 资源中用户ID字段名，默认为'user'
 * @returns {Function} Express中间件函数
 * 
 * @example
 * // 检查借阅记录是否属于当前用户或用户是管理员
 * router.get('/borrowings/:id', protect, ownerOrAdmin('user'), controller);
 */
const ownerOrAdmin = (resourceUserField = 'user') => {
  return async (req, res, next) => {
    try {
      // 检查用户是否已通过认证
      if (!req.user) {
        return res.status(401).json(errorResponse(
          'AUTHENTICATION_REQUIRED',
          '请先登录后再访问此资源',
          401
        ));
      }

      // 如果是管理员，直接允许访问
      if (req.user.role === 'admin') {
        return next();
      }

      // 获取资源ID（通常从路由参数中获取）
      const resourceId = req.params.id || req.params.recordId || req.params.borrowingId;
      
      if (!resourceId) {
        return res.status(400).json(errorResponse(
          'MISSING_RESOURCE_ID',
          '缺少资源ID参数',
          400
        ));
      }

      // 这里需要根据具体的资源类型来查询
      // 由于这是通用中间件，我们将资源查询逻辑留给具体的控制器
      // 或者可以通过req对象传递资源信息
      
      // 将检查逻辑标记传递给下一个中间件
      req.needsOwnershipCheck = {
        resourceId,
        resourceUserField,
        userId: req.user.userId
      };

      next();

    } catch (error) {
      next(error);
    }
  };
};

/**
 * 检查用户是否可以访问特定用户的资源
 * @param {string} targetUserIdParam - 目标用户ID参数名，默认为'userId'
 * @returns {Function} Express中间件函数
 * 
 * @example
 * // 只允许用户访问自己的资料或管理员访问任何用户资料
 * router.get('/users/:userId/profile', protect, canAccessUser('userId'), controller);
 */
const canAccessUser = (targetUserIdParam = 'userId') => {
  return (req, res, next) => {
    // 检查用户是否已通过认证
    if (!req.user) {
      return res.status(401).json(errorResponse(
        'AUTHENTICATION_REQUIRED',
        '请先登录后再访问此资源',
        401
      ));
    }

    // 如果是管理员，允许访问任何用户的资源
    if (req.user.role === 'admin') {
      return next();
    }

    // 获取目标用户ID
    const targetUserId = req.params[targetUserIdParam];
    
    if (!targetUserId) {
      return res.status(400).json(errorResponse(
        'MISSING_USER_ID',
        '缺少用户ID参数',
        400
      ));
    }

    // 检查是否访问自己的资源
    if (req.user.userId.toString() !== targetUserId.toString()) {
      return res.status(403).json(errorResponse(
        'ACCESS_DENIED',
        '您只能访问自己的资源',
        403
      ));
    }

    next();
  };
};

/**
 * 检查用户账户状态中间件
 * 确保用户账户处于激活状态
 */
const requireActiveAccount = (req, res, next) => {
  // 检查用户是否已通过认证
  if (!req.user) {
    return res.status(401).json(errorResponse(
      'AUTHENTICATION_REQUIRED',
      '请先登录后再访问此资源',
      401
    ));
  }

  // 检查账户是否激活
  if (!req.user.isActive) {
    return res.status(403).json(errorResponse(
      'ACCOUNT_DISABLED',
      '您的账户已被禁用，请联系管理员',
      403
    ));
  }

  next();
};

/**
 * 角色权限检查工具函数
 * @param {string} userRole - 用户角色
 * @param {string|Array} requiredRoles - 需要的角色（单个或数组）
 * @returns {boolean} 是否有权限
 */
const hasPermission = (userRole, requiredRoles) => {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  return roles.includes(userRole);
};

/**
 * 角色层级定义
 * 数字越大权限越高
 */
const ROLE_HIERARCHY = {
  member: 1,
  admin: 2
};

/**
 * 检查角色层级权限
 * @param {string} userRole - 用户角色
 * @param {string} requiredRole - 需要的最低角色
 * @returns {boolean} 是否满足权限要求
 */
const hasRoleLevel = (userRole, requiredRole) => {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
  return userLevel >= requiredLevel;
};

/**
 * 最低角色权限中间件
 * @param {string} minRole - 最低要求角色
 * @returns {Function} Express中间件函数
 */
const requireMinRole = (minRole) => {
  return (req, res, next) => {
    // 检查用户是否已通过认证
    if (!req.user) {
      return res.status(401).json(errorResponse(
        'AUTHENTICATION_REQUIRED',
        '请先登录后再访问此资源',
        401
      ));
    }

    // 检查角色层级
    if (!hasRoleLevel(req.user.role, minRole)) {
      return res.status(403).json(errorResponse(
        'INSUFFICIENT_ROLE_LEVEL',
        `访问被拒绝。需要 ${minRole} 或更高级别的角色`,
        403
      ));
    }

    next();
  };
};

module.exports = {
  authorize,
  adminOnly,
  memberAndAbove,
  ownerOrAdmin,
  canAccessUser,
  requireActiveAccount,
  requireMinRole,
  hasPermission,
  hasRoleLevel,
  ROLE_HIERARCHY
};
