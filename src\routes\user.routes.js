/**
 * 用户路由
 * 
 * 定义用户管理相关的路由
 */

const express = require('express');
const router = express.Router();

// 中间件
const { protect } = require('../middlewares/auth.middleware');
const { adminOnly } = require('../middlewares/role.middleware');
const { validate } = require('../middlewares/validation.middleware');

// 控制器
const {
  getAllUsers,
  getUserById,
  updateUserStatus,
  updateUserRole,
  getUserStats,
  updateUserProfile,
  changePassword,
  getUserBorrowingStats
} = require('../controllers/user.controller');

// 验证规则
const { body, param } = require('express-validator');

// 用户ID验证
const userIdValidation = [
  param('id').isMongoId().withMessage('无效的用户ID')
];

// 状态更新验证
const statusUpdateValidation = [
  body('isActive').isBoolean().withMessage('状态必须是布尔值')
];

// 角色更新验证
const roleUpdateValidation = [
  body('role').isIn(['admin', 'member']).withMessage('角色必须是admin或member')
];

// 密码修改验证
const passwordChangeValidation = [
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('新密码长度至少8位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含大小写字母和数字'),
  body('currentPassword')
    .optional()
    .notEmpty()
    .withMessage('当前密码不能为空')
];

// 用户资料更新验证
const profileUpdateValidation = [
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .withMessage('用户名长度必须在3-30个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),
  body('profile.firstName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('名字长度不能超过50个字符'),
  body('profile.lastName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('姓氏长度不能超过50个字符'),
  body('profile.phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('手机号格式不正确'),
  body('profile.address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('地址长度不能超过200个字符')
];

// 路由定义

/**
 * @route GET /api/users
 * @desc 获取所有用户（管理员）
 * @access Private (Admin only)
 */
router.get('/',
  protect,
  adminOnly,
  getAllUsers
);

/**
 * @route GET /api/users/stats
 * @desc 获取用户统计（管理员）
 * @access Private (Admin only)
 */
router.get('/stats',
  protect,
  adminOnly,
  getUserStats
);

/**
 * @route GET /api/users/:id
 * @desc 获取用户详情
 * @access Private (Admin or Self)
 */
router.get('/:id',
  protect,
  userIdValidation,
  validate,
  getUserById
);

/**
 * @route GET /api/users/:id/borrowing-stats
 * @desc 获取用户借阅统计
 * @access Private (Admin or Self)
 */
router.get('/:id/borrowing-stats',
  protect,
  userIdValidation,
  validate,
  getUserBorrowingStats
);

/**
 * @route PATCH /api/users/:id/status
 * @desc 更新用户状态（管理员）
 * @access Private (Admin only)
 */
router.patch('/:id/status',
  protect,
  adminOnly,
  userIdValidation,
  statusUpdateValidation,
  validate,
  updateUserStatus
);

/**
 * @route PATCH /api/users/:id/role
 * @desc 更新用户角色（管理员）
 * @access Private (Admin only)
 */
router.patch('/:id/role',
  protect,
  adminOnly,
  userIdValidation,
  roleUpdateValidation,
  validate,
  updateUserRole
);

/**
 * @route PUT /api/users/:id/profile
 * @desc 更新用户资料
 * @access Private (Admin or Self)
 */
router.put('/:id/profile',
  protect,
  userIdValidation,
  profileUpdateValidation,
  validate,
  updateUserProfile
);

/**
 * @route PATCH /api/users/:id/password
 * @desc 修改密码
 * @access Private (Admin or Self)
 */
router.patch('/:id/password',
  protect,
  userIdValidation,
  passwordChangeValidation,
  validate,
  changePassword
);

module.exports = router;
