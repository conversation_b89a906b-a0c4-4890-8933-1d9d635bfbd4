{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 22:58:05"}
{"level":"info","message":"GET / - ::1","service":"library-management-api","timestamp":"2025-06-21 22:58:05"}
{"level":"info","message":"GET /api - ::1","service":"library-management-api","timestamp":"2025-06-21 22:58:05"}
{"level":"info","message":"GET / - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:07"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:07"}
{"level":"info","message":"GET /health - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:15"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:15"}
{"level":"info","message":"GET / - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:18"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:18"}
{"level":"info","message":"GET / - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:19"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:19"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:59:08"}
{"ip":"::ffff:1********","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:08","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:59:25"}
{"ip":"::ffff:1********","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:25","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:59:43"}
{"ip":"::ffff:1********","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:43","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/auth/register - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:00:30"}
{"level":"info","message":"新用户注册: <EMAIL> (Test12)","service":"library-management-api","timestamp":"2025-06-21 23:00:30"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:00:30"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:01:35"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:04:45"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:04:45"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:04:45"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:04:45"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:04:45"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:04:45"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:05:15"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:05:15"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:05:15"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:05:15"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:05:15"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:05:15"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:05:26"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:05:26"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:05:26"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:05:26"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:05:26"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:05:26"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:05:39"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:05:39"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:05:39"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:05:39"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:05:39"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:05:39"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:05:50"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:05:50"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:05:50"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:05:50"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:05:50"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:05:50"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:06:01"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:06:01"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:06:01"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:06:01"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:06:01"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:06:01"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:06:15"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:06:15"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:06:15"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:06:15"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:06:15"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:06:15"}
{"level":"info","message":"POST /api/auth/logout - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:20"}
{"level":"info","message":"用户登出: <EMAIL>","service":"library-management-api","timestamp":"2025-06-21 23:06:20"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:33"}
{"level":"info","message":"用户登录: <EMAIL> (admin)","service":"library-management-api","timestamp":"2025-06-21 23:06:34"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:34"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:38"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:39"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:39"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:39"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:39"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:40"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:06:42"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:06:42"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:06:42"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:06:42"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:06:42"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:06:42"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:45"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:46"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:48"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:50"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:51"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:52"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:06:53"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:07:10"}
{"ip":"::ffff:1********","level":"error","message":"错误: 令牌无效，用户不存在","method":"GET","service":"library-management-api","stack":"Error: 令牌无效，用户不存在\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:39:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:07:10","url":"/api/borrowings/current","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:08:22"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:08:22"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:08:22"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:08:22"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:08:22"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:08:22"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:09:01"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:09:01"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:09:01"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:09:01"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:09:01"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:09:01"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:09:39"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:09:39"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:09:39"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:09:39"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:09:39"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:09:39"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:09:59"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:09:59"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:09:59"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:09:59"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:09:59"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:09:59"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:04"}
{"level":"info","message":"用户登录: <EMAIL> (admin)","service":"library-management-api","timestamp":"2025-06-21 23:12:04"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:04"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:10"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:10"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:10"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:10"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:11"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:12"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:12"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:12"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:12"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:13"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:15"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:15"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:15"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:15"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:16"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:16"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:17"}
{"level":"info","message":"POST /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:26"}
{"ip":"::ffff:1********","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:26","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:27"}
{"ip":"::ffff:1********","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:27","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:31"}
{"ip":"::ffff:1********","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:31","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:12:32"}
{"ip":"::ffff:1********","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:32","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:14:40"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:14:40"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:14:40"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:14:40"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:14:40"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:14:40"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:16:00"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:16:00"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:16:00"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:16:00"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:16:00"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:16:00"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:16:04"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:16:04"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:16:04"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:16:04"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:16:04"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:16:04"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:16:07"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:16:07"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:16:07"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:16:07"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:16:07"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:16:07"}
{"level":"info","message":"收到SIGINT信号，开始优雅关闭服务器...","service":"library-management-api","timestamp":"2025-06-21 23:16:20"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:16:55"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:16:55"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:16:55"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:16:55"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:16:55"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:16:55"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:17:08"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:17:08"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:17:08"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:17:08"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:17:08"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:17:08"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:09"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:14"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:16"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:16"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:16"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:16"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:17"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:18"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:19"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:19"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:19"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:19"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:20"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:21"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:22"}
{"level":"info","message":"POST /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:28"}
{"level":"info","message":"新图书创建: 1 by admin","service":"library-management-api","timestamp":"2025-06-21 23:17:29"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:29"}
{"level":"info","message":"PATCH /api/books/6856cd08582fbdb2d487e435/stock - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:37"}
{"level":"info","message":"图书库存更新: 1 库存: 0 by admin","service":"library-management-api","timestamp":"2025-06-21 23:17:37"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:37"}
{"level":"info","message":"PATCH /api/books/6856cd08582fbdb2d487e435/stock - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:38"}
{"level":"info","message":"图书库存更新: 1 库存: 1 by admin","service":"library-management-api","timestamp":"2025-06-21 23:17:38"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:38"}
{"level":"info","message":"PUT /api/books/6856cd08582fbdb2d487e435 - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:48"}
{"level":"info","message":"图书更新: 1 by admin","service":"library-management-api","timestamp":"2025-06-21 23:17:48"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:48"}
{"level":"info","message":"GET /api/books/6856cd08582fbdb2d487e435 - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:49"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cd08582fbdb2d487e435 - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:17:59"}
{"ip":"::ffff:1********","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:17:59","url":"/api/borrowings/borrow/6856cd08582fbdb2d487e435","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cd08582fbdb2d487e435 - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:00"}
{"ip":"::ffff:1********","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:18:00","url":"/api/borrowings/borrow/6856cd08582fbdb2d487e435","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:10"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:12"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:19"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:20"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:27"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:27"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:27"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:27"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:28"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:30"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:18:31"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:43"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:43"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:43"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:44"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:44"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:44"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:46"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:46"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:46"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:46"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:46"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:46"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:48"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:48"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:48"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:48"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:48"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:48"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:51"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:51"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:51"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:51"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:51"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:51"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:54"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:54"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:54"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:54"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:54"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:54"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:56"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:56"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:56"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:56"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:56"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:56"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:19:58"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:19:58"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:19:58"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:19:58"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:19:58"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:19:58"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:29"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:31"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:32"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:34"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:37"}
{"ip":"::ffff:1********","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:20:37","url":"/api/borrowings/borrow/6856cda3f287fdb5dd33552d","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"GET /api/books/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:47"}
{"level":"info","message":"POST /api/auth/logout - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:20:52"}
{"level":"info","message":"用户登出: <EMAIL>","service":"library-management-api","timestamp":"2025-06-21 23:20:52"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:21:04"}
{"level":"info","message":"用户登录: <EMAIL> (member)","service":"library-management-api","timestamp":"2025-06-21 23:21:04"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:21:08"}
{"ip":"::ffff:1********","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:21:08","url":"/api/borrowings/borrow/6856cda3f287fdb5dd33552d","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:21:12"}
{"ip":"::ffff:1********","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:21:12","url":"/api/borrowings/borrow/6856cda3f287fdb5dd33552d","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:21:15"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:21:58"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:19"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:19"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:20"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:20"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:20"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:23"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:27"}
{"level":"info","message":"POST /api/auth/logout - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:35"}
{"level":"info","message":"用户登出: <EMAIL>","service":"library-management-api","timestamp":"2025-06-21 23:22:35"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:40"}
{"level":"info","message":"用户登录: <EMAIL> (admin)","service":"library-management-api","timestamp":"2025-06-21 23:22:41"}
{"level":"info","message":"GET /api/books/6856cda3f287fdb5dd335521 - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:44"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:22:51"}
{"level":"info","message":"GET /api/books/6856cda3f287fdb5dd335527 - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:00"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:40"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:40"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:40"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:40"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:41"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:48"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:48"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:48"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:48"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:23:49"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:27:42"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:27:42"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:27:42"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:27:42"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:27:42"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:27:42"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:32:59"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:33:31"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:34:32"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:34:32"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:34:32"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:34:32"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:34:32"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:34:32"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:34:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:35:09"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:09"}
{"level":"info","message":"用户登录: <EMAIL> (admin)","service":"library-management-api","timestamp":"2025-06-21 23:36:09"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:09"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:12"}
{"level":"info","message":"图书借阅: admin 借阅了 设计模式","service":"library-management-api","timestamp":"2025-06-21 23:36:12"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:12"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:17"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:18"}
{"level":"info","message":"PATCH /api/borrowings/6856d16c132beb35cb181495/return - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:34"}
{"level":"info","message":"图书归还: admin 归还了 设计模式","service":"library-management-api","timestamp":"2025-06-21 23:36:34"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:34"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:42"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:42"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:42"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:42"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:36:56"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:40"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:40"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:40"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:40"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:52"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:52"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:52"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:41:52"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:04"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:06"}
{"level":"info","message":"GET /api/books/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:16"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:22"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:28"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:28"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:28"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:28"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:29"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:31"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:42:34"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:43:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:45:31"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:45:31"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:45:31"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:45:31"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:45:31"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:45:31"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:45:32"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:46:26"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:46:58"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:49:00"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:49:00"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:49:00"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:49:00"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:49:00"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:49:00"}
{"level":"info","message":"收到SIGINT信号，开始优雅关闭服务器...","service":"library-management-api","timestamp":"2025-06-21 23:49:12"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:49:18"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:49:18"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:49:18"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:49:18"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:49:18"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:49:18"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:49:31"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:49:31"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:49:31"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:49:31"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:49:31"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:49:31"}
{"level":"info","message":"收到SIGINT信号，开始优雅关闭服务器...","service":"library-management-api","timestamp":"2025-06-21 23:49:56"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:50:11"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:50:11"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:50:11"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:50:11"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:50:11"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:50:11"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:50:24"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:50:24"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:50:24"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:50:24"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:50:24"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:50:24"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:50:40"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:50:40"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:50:40"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:50:40"}
{"level":"info","message":"收到SIGINT信号，开始优雅关闭服务器...","service":"library-management-api","timestamp":"2025-06-21 23:51:09"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:53:57"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:53:57"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:53:57"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:53:57"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:53:57"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:53:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:53:59"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:53:59"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:53:59"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:53:59"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:53:59"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:53:59"}
{"level":"info","message":"GET /api/docs - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:01"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:01"}
{"level":"info","message":"GET /health - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:08"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:08"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:54:10"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:54:10"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:54:10"}
{"level":"info","message":"服务器运行在端口 3001","service":"library-management-api","timestamp":"2025-06-21 23:54:10"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:54:10"}
{"level":"info","message":"API文档: http://localhost:3001/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:54:10"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:15"}
{"level":"info","message":"GET /api/books/stats - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:15"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/books/stats"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:15"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/books?limit=5"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:15"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"level":"info","message":"GET /api/borrowings/overdue - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:15"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/borrowings/overdue"}
{"level":"info","message":"GET /api/borrowings - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:15"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/borrowings?limit=10"}
{"level":"info","message":"GET /api/docs - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"level":"info","message":"GET /api/books/stats - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/books/stats"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/books?limit=5"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"level":"info","message":"GET /api/borrowings/overdue - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/borrowings/overdue"}
{"level":"info","message":"GET /api/borrowings - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:23"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/borrowings?limit=10"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:32"}
{"level":"info","message":"GET /api/books/stats - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:32"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/books/stats"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:32"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/books?limit=5"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:32"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"level":"info","message":"GET /api/borrowings/overdue - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:32"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/borrowings/overdue"}
{"level":"info","message":"GET /api/borrowings - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:32"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/borrowings?limit=10"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:44"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:49"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:49"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:49"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:49"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:53"}
{"level":"info","message":"GET /api/books/stats - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:53"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/books/stats"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:53"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/books?limit=5"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:53"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"level":"info","message":"GET /api/borrowings/overdue - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:53"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/borrowings/overdue"}
{"level":"info","message":"GET /api/borrowings - ::1","service":"library-management-api","timestamp":"2025-06-21 23:54:53"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/borrowings?limit=10"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:54:56"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:00"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:04"}
{"level":"info","message":"POST /api/borrowings/borrow/6856cda3f287fdb5dd33552d - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:07"}
{"level":"info","message":"图书借阅: admin 借阅了 设计模式","service":"library-management-api","timestamp":"2025-06-21 23:55:07"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:07"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:12"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:55:15"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:30"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"服务器运行在端口 3002","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"API文档: http://localhost:3002/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:55:51"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:55:52"}
{"level":"info","message":"收到SIGINT信号，开始优雅关闭服务器...","service":"library-management-api","timestamp":"2025-06-21 23:55:53"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:56:50"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:56:50"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:56:50"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-21 23:56:50"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:56:50"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:56:50"}
{"level":"info","message":"GET /api/docs - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:56:59"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:56:59"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:57:15"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:57:15"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:57:15"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-21 23:57:15"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:57:15"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:57:15"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:57:28"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:57:39"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:57:39"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:57:39"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-21 23:57:39"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:57:39"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:57:39"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:57:40"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:57:40"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:57:40"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:57:40"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 23:57:49"}
{"level":"info","message":"GET /api/books/stats - ::1","service":"library-management-api","timestamp":"2025-06-21 23:57:49"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/books/stats"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:57:49"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/books?limit=5"}
{"level":"info","message":"GET /api/books - ::1","service":"library-management-api","timestamp":"2025-06-21 23:57:49"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"level":"info","message":"GET /api/borrowings/overdue - ::1","service":"library-management-api","timestamp":"2025-06-21 23:57:49"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/borrowings/overdue"}
{"level":"info","message":"GET /api/borrowings - ::1","service":"library-management-api","timestamp":"2025-06-21 23:57:49"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/borrowings?limit=10"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:06"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:10"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:10"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:10"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:10"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:23"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:25"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:27"}
{"level":"info","message":"PATCH /api/borrowings/6856d5dba7bf214df6ac4099/renew - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:45"}
{"ip":"::ffff:1********","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:45","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"PATCH /api/borrowings/6856d5dba7bf214df6ac4099/renew - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:46"}
{"ip":"::ffff:1********","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:46","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:51"}
{"level":"info","message":"PATCH /api/borrowings/6856d5dba7bf214df6ac4099/renew - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:53"}
{"ip":"::ffff:1********","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:53","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"PATCH /api/borrowings/6856d5dba7bf214df6ac4099/renew - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:58:54"}
{"ip":"::ffff:1********","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:54","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:05"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:07"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:08"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:14"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:14"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:14"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:59:14"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:00:18"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:00:18"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:00:18"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:00:18"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:03:55"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:03:55"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:03:55"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:03:55"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:04:06"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:04:12"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:04:14"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:04:15"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:12:23"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:12:23"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:12:23"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:12:23"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:12:23"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:12:23"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:12:34"}
{"level":"info","message":"PATCH /api/borrowings/6856d5dba7bf214df6ac4099/renew - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:12:38"}
{"level":"info","message":"图书续借: admin 续借了 设计模式","service":"library-management-api","timestamp":"2025-06-22 00:12:38"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:12:38"}
{"level":"info","message":"PATCH /api/borrowings/6856d5dba7bf214df6ac4099/renew - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:12:41"}
{"level":"info","message":"图书续借: admin 续借了 设计模式","service":"library-management-api","timestamp":"2025-06-22 00:12:41"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:12:41"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:15:39"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:15:39"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:15:39"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:15:39"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:15:39"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:15:39"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:16:23"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:18:18"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:18:18"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:18:18"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:18:18"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:18:18"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:18:18"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:18:43"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:18:43"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:18:43"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:18:43"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:18:43"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:18:43"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:23:11"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:23:11"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:23:11"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:23:11"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:23:11"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:23:11"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:24:39"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:24:39"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:24:39"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:24:39"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:24:39"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:24:39"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:20"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:22"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:22"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:22"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:22"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:23"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:24"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:28"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:30"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:35"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:38"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:44"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:44"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:44"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:44"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:45"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:46"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:54"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:55"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:56"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:26:57"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:28:31"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:28:31"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:28:31"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:28:31"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:28:31"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:28:31"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:28:47"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:28:47"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:28:47"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:28:47"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:28:47"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:28:47"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:29:02"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:29:02"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:29:02"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:29:02"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:29:02"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:29:02"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:29:17"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:29:17"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:29:17"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:29:17"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:29:17"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:29:17"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:29:32"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:29:32"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:29:32"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:29:32"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:29:32"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:29:32"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:29:46"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:29:46"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:29:46"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:29:46"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:29:46"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:29:46"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:21"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:34"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:34"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:34"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:34"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:37"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:38"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:32:42"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:33:45"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:36:09"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:36:09"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:36:09"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:36:09"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:36:09"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:36:09"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-22 00:36:40"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-22 00:36:40"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-22 00:36:40"}
{"level":"info","message":"服务器运行在端口 3003","service":"library-management-api","timestamp":"2025-06-22 00:36:40"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-22 00:36:40"}
{"level":"info","message":"API文档: http://localhost:3003/api/docs","service":"library-management-api","timestamp":"2025-06-22 00:36:40"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:36:52"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:01"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:03"}
{"level":"info","message":"GET /api/borrowings/my-records - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:16"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:20"}
{"level":"info","message":"GET /api/books/stats - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:22"}
{"level":"info","message":"GET /api/borrowings/overdue - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:22"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:22"}
{"level":"info","message":"GET /api/borrowings - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:22"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:34"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:37"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:39"}
{"level":"info","message":"GET /api/users - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-22 00:37:57"}
