{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 22:56:14"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 22:57:46"}
{"level":"info","message":"GET /health - ::1","service":"library-management-api","timestamp":"2025-06-21 22:58:05"}
{"level":"info","message":"GET / - ::1","service":"library-management-api","timestamp":"2025-06-21 22:58:05"}
{"level":"info","message":"GET /api - ::1","service":"library-management-api","timestamp":"2025-06-21 22:58:05"}
{"level":"info","message":"GET / - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:07"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:07"}
{"level":"info","message":"GET /health - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:15"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:15"}
{"level":"info","message":"GET / - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:18"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:18"}
{"level":"info","message":"GET / - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:19"}
{"level":"info","message":"GET /favicon.ico - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:58:19"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 22:58:28"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:59:08"}
{"ip":"::ffff:1********","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:08","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:59:25"}
{"ip":"::ffff:1********","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:25","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/auth/login - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 22:59:43"}
{"ip":"::ffff:1********","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:43","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"POST /api/auth/register - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:00:30"}
{"level":"info","message":"新用户注册: <EMAIL> (Test12)","service":"library-management-api","timestamp":"2025-06-21 23:00:30"}
{"level":"info","message":"GET /api/books - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:00:30"}
{"level":"info","message":"GET /api/borrowings/current - ::ffff:1********","service":"library-management-api","timestamp":"2025-06-21 23:01:35"}
{"level":"info","message":"Mongoose连接已建立","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"MongoDB连接成功: localhost:27017/library_management","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"数据库连接成功","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"服务器运行在端口 3000","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"环境: development","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
{"level":"info","message":"API文档: http://localhost:3000/api/docs","service":"library-management-api","timestamp":"2025-06-21 23:01:57"}
