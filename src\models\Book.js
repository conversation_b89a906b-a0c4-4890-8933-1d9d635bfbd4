/**
 * 图书模型 (Book Model)
 * 
 * 定义图书数据结构和相关方法
 */

const mongoose = require('mongoose');

const bookSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, '图书标题是必需的'],
    trim: true,
    maxlength: [200, '图书标题不能超过200个字符']
  },
  
  author: {
    type: String,
    required: [true, '作者是必需的'],
    trim: true,
    maxlength: [100, '作者名称不能超过100个字符']
  },
  
  isbn: {
    type: String,
    unique: true,
    sparse: true, // 允许多个文档没有isbn字段
    trim: true,
    match: [
      /^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/,
      '请输入有效的ISBN号码'
    ]
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [1000, '图书描述不能超过1000个字符']
  },
  
  category: {
    type: String,
    required: [true, '图书分类是必需的'],
    trim: true,
    maxlength: [50, '分类名称不能超过50个字符']
  },
  
  publisher: {
    type: String,
    trim: true,
    maxlength: [100, '出版社名称不能超过100个字符']
  },
  
  publishedYear: {
    type: Number,
    min: [1000, '出版年份不能早于1000年'],
    max: [new Date().getFullYear() + 1, '出版年份不能超过明年'],
    validate: {
      validator: Number.isInteger,
      message: '出版年份必须是整数'
    }
  },
  
  language: {
    type: String,
    trim: true,
    maxlength: [30, '语言名称不能超过30个字符']
  },
  
  pages: {
    type: Number,
    min: [1, '页数必须大于0'],
    validate: {
      validator: Number.isInteger,
      message: '页数必须是整数'
    }
  },
  
  stock: {
    type: Number,
    required: [true, '库存数量是必需的'],
    min: [0, '库存数量不能为负数'],
    default: 0,
    validate: {
      validator: Number.isInteger,
      message: '库存数量必须是整数'
    }
  },
  
  totalCopies: {
    type: Number,
    required: [true, '总册数是必需的'],
    min: [1, '总册数必须大于0'],
    validate: {
      validator: Number.isInteger,
      message: '总册数必须是整数'
    }
  },
  
  status: {
    type: String,
    enum: {
      values: ['available', 'borrowed', 'maintenance', 'retired'],
      message: '状态必须是 available, borrowed, maintenance 或 retired'
    },
    default: 'available'
  },
  
  coverImage: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(v);
      },
      message: '封面图片必须是有效的图片URL'
    }
  },
  
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, '标签长度不能超过30个字符']
  }],
  
  // 借阅统计
  borrowingStats: {
    totalBorrowed: {
      type: Number,
      default: 0
    },
    currentBorrowed: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    }
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '创建者是必需的']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段：是否可借
bookSchema.virtual('isAvailable').get(function() {
  return this.status === 'available' && this.stock > 0;
});

// 虚拟字段：借出率
bookSchema.virtual('borrowingRate').get(function() {
  if (this.totalCopies === 0) return 0;
  return ((this.totalCopies - this.stock) / this.totalCopies * 100).toFixed(2);
});

// 索引（isbn的unique索引已在字段定义中设置）
bookSchema.index({ title: 'text', author: 'text', description: 'text' });
bookSchema.index({ category: 1 });
bookSchema.index({ status: 1 });
bookSchema.index({ stock: 1 });
bookSchema.index({ category: 1, status: 1 });
bookSchema.index({ createdBy: 1 });
bookSchema.index({ tags: 1 });
bookSchema.index({ 'borrowingStats.totalBorrowed': -1 });

// 保存前中间件：验证库存不能超过总册数
bookSchema.pre('save', function(next) {
  if (this.stock > this.totalCopies) {
    return next(new Error('库存数量不能超过总册数'));
  }
  
  // 根据库存自动更新状态
  if (this.stock === 0 && this.status === 'available') {
    this.status = 'borrowed';
  } else if (this.stock > 0 && this.status === 'borrowed') {
    this.status = 'available';
  }
  
  next();
});

// 实例方法：借出图书
bookSchema.methods.borrowBook = function() {
  if (this.stock <= 0) {
    throw new Error('图书库存不足');
  }
  
  this.stock -= 1;
  this.borrowingStats.totalBorrowed += 1;
  this.borrowingStats.currentBorrowed += 1;
  
  if (this.stock === 0) {
    this.status = 'borrowed';
  }
  
  return this.save();
};

// 实例方法：归还图书
bookSchema.methods.returnBook = function() {
  if (this.borrowingStats.currentBorrowed <= 0) {
    throw new Error('没有借出的图书可归还');
  }
  
  this.stock += 1;
  this.borrowingStats.currentBorrowed -= 1;
  
  if (this.stock > 0 && this.status === 'borrowed') {
    this.status = 'available';
  }
  
  return this.save();
};

// 静态方法：搜索图书
bookSchema.statics.searchBooks = function(query, options = {}) {
  const {
    page = 1,
    limit = 10,
    category,
    status = 'available',
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = options;
  
  const searchQuery = {
    status: status === 'all' ? { $ne: 'retired' } : status
  };
  
  if (query) {
    searchQuery.$text = { $search: query };
  }
  
  if (category && category !== 'all') {
    searchQuery.category = category;
  }
  
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  return this.find(searchQuery)
    .populate('createdBy', 'username email')
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit);
};

// 静态方法：获取分类统计
bookSchema.statics.getCategoryStats = function() {
  return this.aggregate([
    { $match: { status: { $ne: 'retired' } } },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 },
        availableCount: {
          $sum: { $cond: [{ $eq: ['$status', 'available'] }, 1, 0] }
        },
        totalStock: { $sum: '$stock' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

const Book = mongoose.model('Book', bookSchema);

module.exports = Book;
