/**
 * 借阅历史页面
 * 
 * 显示用户的完整借阅历史记录
 */

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Rate,
  Modal,
  Form,
  Typography,
  Statistic,
  Row,
  Col,
  Image,
} from 'antd';
import {
  SearchOutlined,
  BookOutlined,
  CalendarOutlined,
  StarOutlined,
  HistoryOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { borrowingService } from '../../services/borrowingService';
import { userService } from '../../services/userService';
import { useAuth } from '../../context/AuthContext';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { TextArea } = Input;

function BorrowingHistoryPage() {
  const { user } = useAuth();
  const [searchParams, setSearchParams] = useState({
    search: '',
    status: 'all',
    page: 1,
    limit: 10,
    sortBy: 'borrowDate',
    sortOrder: 'desc',
    startDate: null,
    endDate: null,
  });
  const [ratingModalVisible, setRatingModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [ratingForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取借阅历史
  const {
    data: borrowingHistory,
    isLoading,
    refetch,
  } = useQuery(
    ['borrowingHistory', searchParams],
    () => borrowingService.getBorrowingHistory(searchParams),
    {
      keepPreviousData: true,
    }
  );

  // 获取借阅统计
  const { data: borrowingStats } = useQuery(
    ['userBorrowingStats', user?.userId],
    () => userService.getUserBorrowingStats(user?.userId),
    {
      enabled: !!user?.userId,
      staleTime: 5 * 60 * 1000,
    }
  );

  // 评价图书
  const rateBookMutation = useMutation(
    ({ recordId, rating, review }) => 
      borrowingService.rateBook(recordId, { rating, review }),
    {
      onSuccess: () => {
        toast.success('评价提交成功！');
        setRatingModalVisible(false);
        setSelectedRecord(null);
        ratingForm.resetFields();
        refetch();
      },
      onError: (error) => {
        toast.error(error.response?.data?.error?.message || '评价失败');
      },
    }
  );

  // 处理搜索
  const handleSearch = (value) => {
    setSearchParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // 处理筛选
  const handleFilterChange = (key, value) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setSearchParams(prev => ({
      ...prev,
      startDate: dates?.[0]?.format('YYYY-MM-DD') || null,
      endDate: dates?.[1]?.format('YYYY-MM-DD') || null,
      page: 1,
    }));
  };

  // 处理分页
  const handleTableChange = (pagination, filters, sorter) => {
    setSearchParams(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: sorter.field || 'borrowDate',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    }));
  };

  // 打开评价模态框
  const handleOpenRating = (record) => {
    setSelectedRecord(record);
    setRatingModalVisible(true);
    if (record.rating) {
      ratingForm.setFieldsValue({
        rating: record.rating,
        review: record.review,
      });
    }
  };

  // 提交评价
  const handleSubmitRating = async (values) => {
    rateBookMutation.mutate({
      recordId: selectedRecord._id,
      ...values,
    });
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusConfig = {
      active: { color: 'blue', text: '借阅中' },
      returned: { color: 'green', text: '已归还' },
      overdue: { color: 'red', text: '逾期' },
      lost: { color: 'volcano', text: '丢失' },
    };
    
    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 计算逾期天数
  const getOverdueDays = (dueDate, status) => {
    if (status !== 'active' && status !== 'overdue') return 0;
    const days = Math.ceil((new Date() - new Date(dueDate)) / (1000 * 60 * 60 * 24));
    return Math.max(0, days);
  };

  // 表格列定义
  const columns = [
    {
      title: '图书信息',
      key: 'bookInfo',
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="w-12 h-16 bg-gray-100 rounded flex items-center justify-center">
            {record.book?.coverImage ? (
              <Image
                src={record.book.coverImage}
                alt={record.book.title}
                width={48}
                height={64}
                className="object-cover rounded"
              />
            ) : (
              <BookOutlined className="text-gray-400" />
            )}
          </div>
          <div>
            <div className="font-medium">{record.book?.title}</div>
            <div className="text-gray-500 text-sm">{record.book?.author}</div>
            <div className="text-gray-400 text-xs">{record.book?.category}</div>
          </div>
        </div>
      ),
    },
    {
      title: '借阅日期',
      dataIndex: 'borrowDate',
      key: 'borrowDate',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date, record) => (
        <div>
          <div>{new Date(date).toLocaleDateString()}</div>
          {getOverdueDays(date, record.status) > 0 && (
            <div className="text-red-500 text-xs">
              逾期 {getOverdueDays(date, record.status)} 天
            </div>
          )}
        </div>
      ),
    },
    {
      title: '归还日期',
      dataIndex: 'returnDate',
      key: 'returnDate',
      render: (date) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '续借次数',
      dataIndex: 'renewalCount',
      key: 'renewalCount',
      render: (count) => count || 0,
    },
    {
      title: '罚金',
      dataIndex: 'fine',
      key: 'fine',
      render: (fine) => (
        <span className={fine > 0 ? 'text-red-500' : ''}>
          ¥{fine || 0}
        </span>
      ),
    },
    {
      title: '评价',
      key: 'rating',
      render: (_, record) => (
        <div>
          {record.rating ? (
            <div>
              <Rate disabled value={record.rating} size="small" />
              <div className="text-xs text-gray-500">已评价</div>
            </div>
          ) : record.status === 'returned' ? (
            <Button
              type="link"
              size="small"
              icon={<StarOutlined />}
              onClick={() => handleOpenRating(record)}
            >
              评价
            </Button>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2}>
          <HistoryOutlined className="mr-2" />
          借阅历史
        </Title>
      </div>

      {/* 统计信息 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总借阅次数"
              value={borrowingStats?.data?.totalBorrowed || 0}
              prefix={<BookOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="当前借阅"
              value={borrowingStats?.data?.currentBorrowed || 0}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="逾期次数"
              value={borrowingStats?.data?.overdueCount || 0}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="累计罚金"
              value={borrowingStats?.data?.totalFines || 0}
              prefix="¥"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="搜索图书标题或作者"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
            />
          </Col>
          <Col xs={12} sm={4}>
            <Select
              placeholder="选择状态"
              value={searchParams.status}
              onChange={(value) => handleFilterChange('status', value)}
              className="w-full"
            >
              <Option value="all">所有状态</Option>
              <Option value="active">借阅中</Option>
              <Option value="returned">已归还</Option>
              <Option value="overdue">逾期</Option>
              <Option value="lost">丢失</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6}>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={handleDateRangeChange}
              className="w-full"
            />
          </Col>
          <Col xs={24} sm={6}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchParams({
                    search: '',
                    status: 'all',
                    page: 1,
                    limit: 10,
                    sortBy: 'borrowDate',
                    sortOrder: 'desc',
                    startDate: null,
                    endDate: null,
                  });
                }}
              >
                重置筛选
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 借阅历史表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={borrowingHistory?.data?.items || []}
          loading={isLoading}
          rowKey="_id"
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.limit,
            total: borrowingHistory?.data?.pagination?.totalItems || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 评价模态框 */}
      <Modal
        title="图书评价"
        open={ratingModalVisible}
        onCancel={() => {
          setRatingModalVisible(false);
          setSelectedRecord(null);
          ratingForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        {selectedRecord && (
          <div>
            <div className="mb-4 p-4 bg-gray-50 rounded">
              <div className="font-medium">{selectedRecord.book?.title}</div>
              <div className="text-gray-500 text-sm">{selectedRecord.book?.author}</div>
            </div>

            <Form
              form={ratingForm}
              layout="vertical"
              onFinish={handleSubmitRating}
            >
              <Form.Item
                name="rating"
                label="评分"
                rules={[{ required: true, message: '请给出评分' }]}
              >
                <Rate allowHalf />
              </Form.Item>

              <Form.Item
                name="review"
                label="评价内容"
                rules={[
                  { max: 500, message: '评价内容不能超过500个字符' },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder="分享您对这本书的看法..."
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={rateBookMutation.isLoading}
                  >
                    提交评价
                  </Button>
                  <Button
                    onClick={() => {
                      setRatingModalVisible(false);
                      setSelectedRecord(null);
                      ratingForm.resetFields();
                    }}
                  >
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default BorrowingHistoryPage;
