/**
 * 图书管理和借阅功能测试脚本
 * 
 * 测试图书CRUD操作和借阅功能
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// 测试用户数据
const testUsers = {
  admin: {
    username: 'bookadmin',
    email: '<EMAIL>',
    password: 'Admin123456'
  },
  member: {
    username: 'bookmember',
    email: '<EMAIL>',
    password: 'Member123456'
  }
};

// 测试图书数据
const testBook = {
  title: 'JavaScript高级程序设计',
  author: '<PERSON>',
  isbn: '9787115275790',
  description: '深入理解JavaScript的经典教程',
  category: '计算机技术',
  publisher: '人民邮电出版社',
  publishedYear: 2012,
  language: 'Chinese',
  pages: 800,
  totalCopies: 5,
  stock: 5,
  tags: ['JavaScript', '前端开发', '编程']
};

let tokens = { admin: '', member: '' };
let testBookId = '';
let borrowingRecordId = '';

/**
 * 设置测试用户
 */
async function setupTestUsers() {
  console.log('🔧 设置测试用户...\n');

  for (const [role, userData] of Object.entries(testUsers)) {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
      tokens[role] = response.data.data.tokens.accessToken;
      console.log(`✅ ${role}用户创建成功`);
    } catch (error) {
      if (error.response?.data?.error?.code === 'USER_ALREADY_EXISTS') {
        try {
          const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
            email: userData.email,
            password: userData.password
          });
          tokens[role] = loginResponse.data.data.tokens.accessToken;
          console.log(`✅ ${role}用户登录成功`);
        } catch (loginError) {
          console.error(`❌ ${role}用户登录失败:`, loginError.response?.data?.error?.message);
        }
      }
    }
  }
}

/**
 * 测试创建图书（管理员权限）
 */
async function testCreateBook() {
  console.log('\n🧪 测试创建图书...');

  if (!tokens.admin) {
    console.log('❌ 没有管理员令牌');
    return;
  }

  try {
    const response = await axios.post(`${API_BASE_URL}/books`, testBook, {
      headers: { Authorization: `Bearer ${tokens.admin}` }
    });

    testBookId = response.data.data._id;
    console.log('✅ 图书创建成功:', response.data.data.title);
    console.log('📚 图书ID:', testBookId);
  } catch (error) {
    console.error('❌ 图书创建失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试获取图书列表
 */
async function testGetBooks() {
  console.log('\n🧪 测试获取图书列表...');

  if (!tokens.member) {
    console.log('❌ 没有会员令牌');
    return;
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/books?page=1&limit=10`, {
      headers: { Authorization: `Bearer ${tokens.member}` }
    });

    console.log('✅ 获取图书列表成功');
    console.log('📊 总数:', response.data.data.pagination.totalItems);
    console.log('📚 图书数量:', response.data.data.items.length);
  } catch (error) {
    console.error('❌ 获取图书列表失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试获取单本图书详情
 */
async function testGetBookById() {
  console.log('\n🧪 测试获取图书详情...');

  if (!tokens.member || !testBookId) {
    console.log('❌ 缺少必要的令牌或图书ID');
    return;
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/books/${testBookId}`, {
      headers: { Authorization: `Bearer ${tokens.member}` }
    });

    console.log('✅ 获取图书详情成功:', response.data.data.title);
    console.log('📖 作者:', response.data.data.author);
    console.log('📦 库存:', response.data.data.stock);
  } catch (error) {
    console.error('❌ 获取图书详情失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试借阅图书
 */
async function testBorrowBook() {
  console.log('\n🧪 测试借阅图书...');

  if (!tokens.member || !testBookId) {
    console.log('❌ 缺少必要的令牌或图书ID');
    return;
  }

  try {
    const response = await axios.post(`${API_BASE_URL}/borrowings/borrow/${testBookId}`, {}, {
      headers: { Authorization: `Bearer ${tokens.member}` }
    });

    borrowingRecordId = response.data.data._id;
    console.log('✅ 图书借阅成功');
    console.log('📋 借阅记录ID:', borrowingRecordId);
    console.log('📅 应还日期:', response.data.data.dueDate);
  } catch (error) {
    console.error('❌ 图书借阅失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试获取当前借阅
 */
async function testGetCurrentBorrowings() {
  console.log('\n🧪 测试获取当前借阅...');

  if (!tokens.member) {
    console.log('❌ 没有会员令牌');
    return;
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/borrowings/current`, {
      headers: { Authorization: `Bearer ${tokens.member}` }
    });

    console.log('✅ 获取当前借阅成功');
    console.log('📚 当前借阅数量:', response.data.data.length);
    if (response.data.data.length > 0) {
      console.log('📖 借阅的图书:', response.data.data.map(b => b.book.title).join(', '));
    }
  } catch (error) {
    console.error('❌ 获取当前借阅失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试续借图书
 */
async function testRenewBook() {
  console.log('\n🧪 测试续借图书...');

  if (!tokens.member || !borrowingRecordId) {
    console.log('❌ 缺少必要的令牌或借阅记录ID');
    return;
  }

  try {
    const response = await axios.patch(`${API_BASE_URL}/borrowings/${borrowingRecordId}/renew`, {}, {
      headers: { Authorization: `Bearer ${tokens.member}` }
    });

    console.log('✅ 图书续借成功');
    console.log('📅 新的应还日期:', response.data.data.dueDate);
    console.log('🔄 续借次数:', response.data.data.renewalCount);
  } catch (error) {
    console.error('❌ 图书续借失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试归还图书
 */
async function testReturnBook() {
  console.log('\n🧪 测试归还图书...');

  if (!tokens.member || !borrowingRecordId) {
    console.log('❌ 缺少必要的令牌或借阅记录ID');
    return;
  }

  try {
    const response = await axios.patch(`${API_BASE_URL}/borrowings/${borrowingRecordId}/return`, {}, {
      headers: { Authorization: `Bearer ${tokens.member}` }
    });

    console.log('✅ 图书归还成功');
    console.log('📅 归还日期:', response.data.data.returnDate);
    console.log('💰 罚金:', response.data.data.fine || 0);
  } catch (error) {
    console.error('❌ 图书归还失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试更新图书（管理员权限）
 */
async function testUpdateBook() {
  console.log('\n🧪 测试更新图书...');

  if (!tokens.admin || !testBookId) {
    console.log('❌ 缺少必要的令牌或图书ID');
    return;
  }

  const updateData = {
    description: '更新后的图书描述 - JavaScript高级程序设计第四版',
    stock: 3
  };

  try {
    const response = await axios.put(`${API_BASE_URL}/books/${testBookId}`, updateData, {
      headers: { Authorization: `Bearer ${tokens.admin}` }
    });

    console.log('✅ 图书更新成功');
    console.log('📝 新描述:', response.data.data.description);
    console.log('📦 新库存:', response.data.data.stock);
  } catch (error) {
    console.error('❌ 图书更新失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试获取图书统计（管理员权限）
 */
async function testGetBookStats() {
  console.log('\n🧪 测试获取图书统计...');

  if (!tokens.admin) {
    console.log('❌ 没有管理员令牌');
    return;
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/books/stats`, {
      headers: { Authorization: `Bearer ${tokens.admin}` }
    });

    console.log('✅ 获取图书统计成功');
    console.log('📊 统计信息:', response.data.data.overview);
  } catch (error) {
    console.error('❌ 获取图书统计失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试权限控制
 */
async function testPermissions() {
  console.log('\n🧪 测试权限控制...');

  // 测试会员尝试创建图书（应该被拒绝）
  if (tokens.member) {
    try {
      await axios.post(`${API_BASE_URL}/books`, testBook, {
        headers: { Authorization: `Bearer ${tokens.member}` }
      });
      console.log('❌ 会员创建图书应该被拒绝但却成功了');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ 会员创建图书正确被拒绝');
      }
    }
  }

  // 测试无令牌访问（应该被拒绝）
  try {
    await axios.get(`${API_BASE_URL}/books`);
    console.log('❌ 无令牌访问应该被拒绝但却成功了');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ 无令牌访问正确被拒绝');
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始图书管理和借阅功能测试\n');

  await setupTestUsers();
  await testCreateBook();
  await testGetBooks();
  await testGetBookById();
  await testBorrowBook();
  await testGetCurrentBorrowings();
  await testRenewBook();
  await testReturnBook();
  await testUpdateBook();
  await testGetBookStats();
  await testPermissions();

  console.log('\n🎉 图书管理和借阅功能测试完成');
  console.log('\n📊 测试总结:');
  console.log(`- 管理员令牌: ${tokens.admin ? '✅ 已获取' : '❌ 未获取'}`);
  console.log(`- 会员令牌: ${tokens.member ? '✅ 已获取' : '❌ 未获取'}`);
  console.log(`- 测试图书ID: ${testBookId || '未创建'}`);
  console.log(`- 借阅记录ID: ${borrowingRecordId || '未创建'}`);
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  setupTestUsers,
  testCreateBook,
  testGetBooks,
  testBorrowBook,
  testReturnBook,
  runAllTests
};
