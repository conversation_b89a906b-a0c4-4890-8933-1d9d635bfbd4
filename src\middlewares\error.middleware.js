/**
 * 全局错误处理中间件
 * 
 * 统一处理应用中的所有错误
 */

const logger = require('../utils/logger');
const { errorResponse } = require('../utils/response');

/**
 * 全局错误处理中间件
 * @param {Error} err - 错误对象
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  logger.error(`错误: ${err.message}`, {
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Mongoose验证错误
  if (err.name === 'ValidationError') {
    const message = '数据验证失败';
    const details = Object.values(err.errors).map(val => ({
      field: val.path,
      message: val.message
    }));
    return res.status(400).json(errorResponse('VALIDATION_ERROR', message, 400, details));
  }

  // Mongoose重复键错误
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const message = `${field} 已存在`;
    return res.status(409).json(errorResponse('DUPLICATE_ERROR', message, 409));
  }

  // Mongoose转换错误
  if (err.name === 'CastError') {
    const message = '资源未找到';
    return res.status(404).json(errorResponse('NOT_FOUND', message, 404));
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    const message = '无效的访问令牌';
    return res.status(401).json(errorResponse('INVALID_TOKEN', message, 401));
  }

  // JWT过期错误
  if (err.name === 'TokenExpiredError') {
    const message = '访问令牌已过期';
    return res.status(401).json(errorResponse('TOKEN_EXPIRED', message, 401));
  }

  // 自定义应用错误
  if (err.isOperational) {
    return res.status(err.statusCode || 500).json(
      errorResponse(err.code || 'APPLICATION_ERROR', err.message, err.statusCode || 500)
    );
  }

  // 默认服务器错误
  const message = process.env.NODE_ENV === 'production' 
    ? '服务器内部错误' 
    : err.message;

  res.status(500).json(errorResponse('INTERNAL_SERVER_ERROR', message, 500));
};

/**
 * 自定义应用错误类
 */
class AppError extends Error {
  constructor(message, statusCode, code = 'APPLICATION_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 异步错误捕获包装器
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  errorHandler,
  AppError,
  asyncHandler
};
