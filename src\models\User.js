/**
 * 用户模型 (User Model)
 * 
 * 定义用户数据结构和相关方法
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, '用户名是必需的'],
    unique: true,
    trim: true,
    minlength: [3, '用户名至少需要3个字符'],
    maxlength: [30, '用户名不能超过30个字符'],
    match: [/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线']
  },
  
  email: {
    type: String,
    required: [true, '邮箱是必需的'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      '请输入有效的邮箱地址'
    ]
  },
  
  password: {
    type: String,
    required: [true, '密码是必需的'],
    minlength: [6, '密码至少需要6个字符'],
    select: false // 默认查询时不返回密码字段
  },
  
  role: {
    type: String,
    enum: {
      values: ['admin', 'member'],
      message: '角色必须是 admin 或 member'
    },
    default: 'member'
  },
  
  profile: {
    firstName: {
      type: String,
      trim: true,
      maxlength: [50, '名字不能超过50个字符']
    },
    lastName: {
      type: String,
      trim: true,
      maxlength: [50, '姓氏不能超过50个字符']
    },
    phone: {
      type: String,
      trim: true,
      match: [/^[0-9-+\s()]+$/, '请输入有效的电话号码']
    },
    address: {
      type: String,
      trim: true,
      maxlength: [200, '地址不能超过200个字符']
    }
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  lastLoginAt: {
    type: Date
  },
  
  // 借阅统计信息
  borrowingStats: {
    totalBorrowed: {
      type: Number,
      default: 0
    },
    currentBorrowed: {
      type: Number,
      default: 0
    },
    overdueFines: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true, // 自动添加 createdAt 和 updatedAt
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// 虚拟字段：全名
userSchema.virtual('fullName').get(function() {
  if (this.profile.firstName && this.profile.lastName) {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }
  return this.username;
});

// 索引（email和username的unique索引已在字段定义中设置）
userSchema.index({ role: 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ 'borrowingStats.currentBorrowed': 1 });
userSchema.index({ lastLoginAt: -1 });

// 保存前中间件：密码哈希
userSchema.pre('save', async function(next) {
  // 只有密码被修改时才进行哈希
  if (!this.isModified('password')) return next();
  
  try {
    // 生成盐值并哈希密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.password = await bcrypt.hash(this.password, saltRounds);
    next();
  } catch (error) {
    next(error);
  }
});

// 实例方法：验证密码
userSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw new Error('密码验证失败');
  }
};

// 实例方法：更新最后登录时间
userSchema.methods.updateLastLogin = function() {
  this.lastLoginAt = new Date();
  return this.save({ validateBeforeSave: false });
};

// 实例方法：检查是否可以借阅更多图书
userSchema.methods.canBorrowMore = function() {
  const maxBooks = parseInt(process.env.MAX_BOOKS_PER_USER) || 5;
  return this.borrowingStats.currentBorrowed < maxBooks;
};

// 静态方法：根据邮箱查找用户（包含密码）
userSchema.statics.findByEmailWithPassword = function(email) {
  return this.findOne({ email }).select('+password');
};

// 静态方法：获取活跃用户统计
userSchema.statics.getActiveUsersCount = function() {
  return this.countDocuments({ isActive: true });
};

const User = mongoose.model('User', userSchema);

module.exports = User;
