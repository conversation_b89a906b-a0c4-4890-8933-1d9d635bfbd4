import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  message,
  Tooltip,
  Image,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  BookOutlined,
  UserOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { borrowingService } from '../../services/borrowingService';
import { useSearchParams } from 'react-router-dom';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { confirm } = Modal;

function AdminBorrowingsPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [editForm] = Form.useForm();

  // 获取URL参数
  const status = searchParams.get('status') || 'all';
  const search = searchParams.get('search') || '';
  const page = parseInt(searchParams.get('page')) || 1;
  const limit = parseInt(searchParams.get('limit')) || 10;

  // 查询参数
  const queryParams = {
    status: status === 'all' ? undefined : status,
    search,
    page,
    limit,
    sortBy: 'borrowDate',
    sortOrder: 'desc',
  };

  // 获取借阅记录
  const {
    data: borrowingRecords,
    isLoading,
    refetch,
  } = useQuery(
    ['adminBorrowings', queryParams],
    () => borrowingService.getAllBorrowingRecords(queryParams),
    {
      keepPreviousData: true,
    }
  );

  // 获取借阅统计
  const { data: borrowingStats } = useQuery(
    ['adminBorrowingStats'],
    () => borrowingService.getBorrowingStats(),
    {
      staleTime: 5 * 60 * 1000,
    }
  );

  // 更新借阅记录
  const updateRecordMutation = useMutation({
    mutationFn: ({ recordId, data }) => borrowingService.updateBorrowingRecord(recordId, data),
    onSuccess: () => {
      message.success('借阅记录更新成功');
      setEditModalVisible(false);
      setSelectedRecord(null);
      editForm.resetFields();
      queryClient.invalidateQueries(['adminBorrowings']);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || '更新失败');
    },
  });

  // 处理搜索
  const handleSearch = (value) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set('search', value);
    } else {
      newParams.delete('search');
    }
    newParams.set('page', '1');
    setSearchParams(newParams);
  };

  // 处理状态筛选
  const handleStatusFilter = (value) => {
    const newParams = new URLSearchParams(searchParams);
    if (value && value !== 'all') {
      newParams.set('status', value);
    } else {
      newParams.delete('status');
    }
    newParams.set('page', '1');
    setSearchParams(newParams);
  };

  // 处理表格变化
  const handleTableChange = (pagination) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('page', pagination.current.toString());
    newParams.set('limit', pagination.pageSize.toString());
    setSearchParams(newParams);
  };

  // 打开编辑模态框
  const handleEdit = (record) => {
    setSelectedRecord(record);
    setEditModalVisible(true);
    editForm.setFieldsValue({
      status: record.status,
      dueDate: record.dueDate,
      fine: record.fine,
      notes: record.notes,
    });
  };

  // 处理编辑提交
  const handleEditSubmit = (values) => {
    updateRecordMutation.mutate({
      recordId: selectedRecord._id,
      data: values,
    });
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusConfig = {
      active: { color: 'blue', text: '借阅中' },
      returned: { color: 'green', text: '已归还' },
      overdue: { color: 'red', text: '逾期' },
      lost: { color: 'volcano', text: '丢失' },
    };
    
    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 计算逾期天数
  const getOverdueDays = (dueDate, status) => {
    if (status !== 'active' && status !== 'overdue') return 0;
    const days = Math.ceil((new Date() - new Date(dueDate)) / (1000 * 60 * 60 * 24));
    return Math.max(0, days);
  };

  // 表格列定义
  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.user?.username}</div>
          <div className="text-gray-500 text-sm">{record.user?.email}</div>
        </div>
      ),
    },
    {
      title: '图书信息',
      key: 'bookInfo',
      width: 250,
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-12 bg-gray-100 rounded flex items-center justify-center">
            {record.book?.coverImage ? (
              <Image
                src={record.book.coverImage}
                alt={record.book.title}
                width={40}
                height={48}
                className="object-cover rounded"
              />
            ) : (
              <BookOutlined className="text-gray-400" />
            )}
          </div>
          <div>
            <div className="font-medium">{record.book?.title}</div>
            <div className="text-gray-500 text-sm">{record.book?.author}</div>
          </div>
        </div>
      ),
    },
    {
      title: '借阅日期',
      dataIndex: 'borrowDate',
      key: 'borrowDate',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      render: (date, record) => (
        <div>
          <div>{new Date(date).toLocaleDateString()}</div>
          {getOverdueDays(date, record.status) > 0 && (
            <div className="text-red-500 text-xs">
              逾期 {getOverdueDays(date, record.status)} 天
            </div>
          )}
        </div>
      ),
    },
    {
      title: '归还日期',
      dataIndex: 'returnDate',
      key: 'returnDate',
      width: 120,
      render: (date) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '续借次数',
      dataIndex: 'renewalCount',
      key: 'renewalCount',
      width: 100,
      render: (count) => count || 0,
    },
    {
      title: '罚金',
      dataIndex: 'fine',
      key: 'fine',
      width: 80,
      render: (fine) => (
        <span className={fine > 0 ? 'text-red-500' : ''}>
          ¥{fine || 0}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                // 查看详情逻辑
                console.log('View details:', record);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2}>
          <BookOutlined className="mr-2" />
          借阅管理
        </Title>
      </div>

      {/* 统计信息 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总借阅记录"
              value={borrowingStats?.data?.totalBorrowings || 0}
              prefix={<BookOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃借阅"
              value={borrowingStats?.data?.activeBorrowings || 0}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="逾期记录"
              value={borrowingStats?.data?.overdueBorrowings || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总罚金"
              value={borrowingStats?.data?.totalFines || 0}
              prefix="¥"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="搜索用户或图书"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
              defaultValue={search}
            />
          </Col>
          <Col xs={12} sm={4}>
            <Select
              placeholder="选择状态"
              value={status}
              onChange={handleStatusFilter}
              className="w-full"
            >
              <Option value="all">所有状态</Option>
              <Option value="active">借阅中</Option>
              <Option value="returned">已归还</Option>
              <Option value="overdue">逾期</Option>
              <Option value="lost">丢失</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6}>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              className="w-full"
            />
          </Col>
          <Col xs={24} sm={6}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchParams({});
                }}
              >
                重置筛选
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 借阅记录表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={borrowingRecords?.data?.items || []}
          loading={isLoading}
          rowKey="_id"
          pagination={{
            current: page,
            pageSize: limit,
            total: borrowingRecords?.data?.pagination?.totalItems || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑借阅记录"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedRecord(null);
          editForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        {selectedRecord && (
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleEditSubmit}
          >
            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select>
                <Option value="active">借阅中</Option>
                <Option value="returned">已归还</Option>
                <Option value="overdue">逾期</Option>
                <Option value="lost">丢失</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="dueDate"
              label="应还日期"
              rules={[{ required: true, message: '请选择应还日期' }]}
            >
              <DatePicker className="w-full" />
            </Form.Item>

            <Form.Item
              name="fine"
              label="罚金"
              rules={[{ type: 'number', min: 0, message: '罚金不能为负数' }]}
            >
              <Input type="number" prefix="¥" placeholder="0" />
            </Form.Item>

            <Form.Item name="notes" label="备注">
              <Input.TextArea rows={3} placeholder="添加备注..." />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={updateRecordMutation.isLoading}
                >
                  保存
                </Button>
                <Button
                  onClick={() => {
                    setEditModalVisible(false);
                    setSelectedRecord(null);
                    editForm.resetFields();
                  }}
                >
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
}

export default AdminBorrowingsPage;
