/**
 * 主布局组件
 * 
 * 应用程序的主要布局，包含侧边栏导航和顶部栏
 */

import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Typography,
  Space,
  Drawer,
} from 'antd';
import {
  BookOutlined,
  ReadOutlined,
  UserOutlined,
  DashboardOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../../context/AuthContext';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

function MainLayout() {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const { user, logout, isAdmin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 处理登出
  const handleLogout = async () => {
    await logout();
    navigate('/auth/login');
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  // 侧边栏菜单项
  const menuItems = [
    {
      key: '/books',
      icon: <BookOutlined />,
      label: '图书管理',
      children: [
        {
          key: '/books',
          label: '图书列表',
        },
        ...(isAdmin() ? [
          {
            key: '/admin/books',
            label: '图书管理',
          },
        ] : []),
      ],
    },
    {
      key: '/borrowings',
      icon: <ReadOutlined />,
      label: '借阅管理',
      children: [
        {
          key: '/borrowings',
          label: '我的借阅',
        },
        {
          key: '/borrowings/history',
          label: '借阅历史',
        },
      ],
    },
    ...(isAdmin() ? [
      {
        key: '/admin',
        icon: <DashboardOutlined />,
        label: '系统管理',
        children: [
          {
            key: '/admin',
            label: '管理面板',
          },
          {
            key: '/admin/users',
            label: '用户管理',
          },
          {
            key: '/admin/borrowings',
            label: '借阅管理',
          },
          {
            key: '/admin/statistics',
            label: '统计报表',
          },
        ],
      },
    ] : []),
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 处理菜单点击
  const handleMenuClick = ({ key }) => {
    navigate(key);
    setMobileDrawerVisible(false);
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;

    // 首先尝试精确匹配
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (pathname === child.key) {
            return [child.key];
          }
        }
      } else if (pathname === item.key) {
        return [item.key];
      }
    }

    // 然后尝试前缀匹配（但要避免冲突）
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          // 特殊处理：避免 /borrowings 匹配到 /borrowings/history
          if (child.key === '/borrowings' && pathname === '/borrowings/history') {
            continue;
          }
          if (pathname.startsWith(child.key + '/')) {
            return [child.key];
          }
        }
      } else if (pathname.startsWith(item.key + '/')) {
        return [item.key];
      }
    }

    return [pathname];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const pathname = location.pathname;
    const openKeys = [];
    
    for (const item of menuItems) {
      if (item.children) {
        for (const child of item.children) {
          if (pathname === child.key || pathname.startsWith(child.key + '/')) {
            openKeys.push(item.key);
            break;
          }
        }
      }
    }
    
    return openKeys;
  };

  // 侧边栏内容
  const siderContent = (
    <div className="h-full flex flex-col">
      {/* Logo区域 */}
      <div className="h-16 flex items-center justify-center border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <BookOutlined className="text-2xl text-blue-500" />
          {!collapsed && (
            <Text className="text-lg font-semibold text-white">
              图书管理系统
            </Text>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-r-0"
        />
      </div>

      {/* 用户信息区域 */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center space-x-3">
            <Avatar size="small" icon={<UserOutlined />} />
            <div className="flex-1 min-w-0">
              <Text className="text-white text-sm font-medium block truncate">
                {user?.profile?.firstName || user?.username}
              </Text>
              <Text className="text-gray-300 text-xs block truncate">
                {user?.role === 'admin' ? '管理员' : '会员'}
              </Text>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <Layout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="hidden lg:block"
        width={256}
        collapsedWidth={80}
      >
        {siderContent}
      </Sider>

      {/* 移动端抽屉 */}
      <Drawer
        title={
          <div className="flex items-center space-x-2">
            <BookOutlined className="text-xl text-blue-500" />
            <span>图书管理系统</span>
          </div>
        }
        placement="left"
        onClose={() => setMobileDrawerVisible(false)}
        open={mobileDrawerVisible}
        className="lg:hidden"
        width={256}
        bodyStyle={{ padding: 0 }}
      >
        <Menu
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Drawer>

      <Layout>
        {/* 顶部栏 */}
        <Header className="bg-white shadow-sm px-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 折叠按钮 */}
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="hidden lg:flex"
            />
            
            {/* 移动端菜单按钮 */}
            <Button
              type="text"
              icon={<MenuFoldOutlined />}
              onClick={() => setMobileDrawerVisible(true)}
              className="lg:hidden"
            />

            {/* 搜索按钮 */}
            <Button
              type="text"
              icon={<SearchOutlined />}
              onClick={() => navigate('/books')}
            >
              搜索图书
            </Button>
          </div>

          <div className="flex items-center space-x-4">
            {/* 通知 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="flex items-center justify-center"
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <Avatar size="small" icon={<UserOutlined />} />
                <div className="hidden sm:block">
                  <Text className="text-sm font-medium">
                    {user?.profile?.firstName || user?.username}
                  </Text>
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content className="m-6 p-6 bg-white rounded-lg shadow-sm min-h-[calc(100vh-112px)]">
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}

export default MainLayout;
