/**
 * 借阅记录模型 (BorrowingRecord Model)
 * 
 * 定义借阅记录数据结构和相关方法
 */

const mongoose = require('mongoose');

const borrowingRecordSchema = new mongoose.Schema({
  book: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Book',
    required: [true, '图书ID是必需的']
  },
  
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必需的']
  },
  
  borrowDate: {
    type: Date,
    required: [true, '借阅日期是必需的'],
    default: Date.now
  },
  
  dueDate: {
    type: Date,
    required: [true, '应还日期是必需的']
  },
  
  returnDate: {
    type: Date,
    validate: {
      validator: function(v) {
        return !v || v >= this.borrowDate;
      },
      message: '归还日期不能早于借阅日期'
    }
  },
  
  status: {
    type: String,
    enum: {
      values: ['active', 'returned', 'overdue', 'lost'],
      message: '状态必须是 active, returned, overdue 或 lost'
    },
    default: 'active'
  },
  
  renewalCount: {
    type: Number,
    default: 0,
    min: [0, '续借次数不能为负数'],
    max: [5, '续借次数不能超过5次'],
    validate: {
      validator: Number.isInteger,
      message: '续借次数必须是整数'
    }
  },
  
  fine: {
    type: Number,
    default: 0,
    min: [0, '罚金不能为负数']
  },
  
  notes: {
    type: String,
    trim: true,
    maxlength: [500, '备注不能超过500个字符']
  },
  
  // 续借历史
  renewalHistory: [{
    renewalDate: {
      type: Date,
      default: Date.now
    },
    previousDueDate: Date,
    newDueDate: Date,
    reason: String
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段：是否逾期
borrowingRecordSchema.virtual('isOverdue').get(function() {
  if (this.status === 'returned') return false;
  return new Date() > this.dueDate;
});

// 虚拟字段：逾期天数
borrowingRecordSchema.virtual('overdueDays').get(function() {
  if (!this.isOverdue) return 0;
  const today = new Date();
  const diffTime = today - this.dueDate;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// 虚拟字段：借阅天数
borrowingRecordSchema.virtual('borrowingDays').get(function() {
  const endDate = this.returnDate || new Date();
  const diffTime = endDate - this.borrowDate;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// 索引
borrowingRecordSchema.index({ user: 1 });
borrowingRecordSchema.index({ book: 1 });
borrowingRecordSchema.index({ status: 1 });
borrowingRecordSchema.index({ borrowDate: 1 });
borrowingRecordSchema.index({ dueDate: 1 });
borrowingRecordSchema.index({ user: 1, status: 1 });
borrowingRecordSchema.index({ status: 1, dueDate: 1 });

// 保存前中间件：设置应还日期
borrowingRecordSchema.pre('save', function(next) {
  // 如果是新记录且没有设置应还日期，则自动计算
  if (this.isNew && !this.dueDate) {
    const borrowingPeriod = parseInt(process.env.DEFAULT_BORROWING_PERIOD) || 30;
    this.dueDate = new Date(this.borrowDate.getTime() + borrowingPeriod * 24 * 60 * 60 * 1000);
  }
  
  // 自动更新状态
  if (this.returnDate && this.status === 'active') {
    this.status = 'returned';
  } else if (!this.returnDate && new Date() > this.dueDate && this.status === 'active') {
    this.status = 'overdue';
  }
  
  next();
});

// 保存后中间件：更新用户和图书的统计信息
borrowingRecordSchema.post('save', async function(doc) {
  try {
    const User = mongoose.model('User');
    const Book = mongoose.model('Book');
    
    // 更新用户借阅统计
    const userStats = await BorrowingRecord.aggregate([
      { $match: { user: doc.user } },
      {
        $group: {
          _id: null,
          totalBorrowed: { $sum: 1 },
          currentBorrowed: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          overdueFines: { $sum: '$fine' }
        }
      }
    ]);
    
    if (userStats.length > 0) {
      await User.findByIdAndUpdate(doc.user, {
        borrowingStats: userStats[0]
      });
    }
    
  } catch (error) {
    console.error('更新统计信息失败:', error);
  }
});

// 实例方法：续借
borrowingRecordSchema.methods.renew = function(additionalDays = null) {
  const maxRenewals = parseInt(process.env.MAX_RENEWALS) || 2;
  
  if (this.renewalCount >= maxRenewals) {
    throw new Error(`已达到最大续借次数 (${maxRenewals})`);
  }
  
  if (this.status !== 'active') {
    throw new Error('只有活跃状态的借阅记录才能续借');
  }
  
  const borrowingPeriod = additionalDays || parseInt(process.env.DEFAULT_BORROWING_PERIOD) || 30;
  const previousDueDate = new Date(this.dueDate);
  const newDueDate = new Date(this.dueDate.getTime() + borrowingPeriod * 24 * 60 * 60 * 1000);
  
  // 记录续借历史
  this.renewalHistory.push({
    renewalDate: new Date(),
    previousDueDate,
    newDueDate,
    reason: '用户申请续借'
  });
  
  this.dueDate = newDueDate;
  this.renewalCount += 1;
  
  return this.save();
};

// 实例方法：归还
borrowingRecordSchema.methods.returnBook = function() {
  if (this.status === 'returned') {
    throw new Error('图书已经归还');
  }
  
  this.returnDate = new Date();
  this.status = 'returned';
  
  // 计算逾期罚金
  if (this.isOverdue) {
    const finePerDay = parseFloat(process.env.FINE_PER_DAY) || 1.0;
    this.fine = this.overdueDays * finePerDay;
  }
  
  return this.save();
};

// 静态方法：获取逾期记录
borrowingRecordSchema.statics.getOverdueRecords = function() {
  return this.find({
    status: 'active',
    dueDate: { $lt: new Date() }
  })
  .populate('user', 'username email profile')
  .populate('book', 'title author isbn');
};

// 静态方法：获取用户借阅历史
borrowingRecordSchema.statics.getUserBorrowingHistory = function(userId, options = {}) {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'borrowDate',
    sortOrder = 'desc'
  } = options;
  
  const query = { user: userId };
  if (status && status !== 'all') {
    query.status = status;
  }
  
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  return this.find(query)
    .populate('book', 'title author isbn coverImage')
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit);
};

// 静态方法：获取借阅统计
borrowingRecordSchema.statics.getBorrowingStats = function(startDate, endDate) {
  const matchQuery = {};
  
  if (startDate || endDate) {
    matchQuery.borrowDate = {};
    if (startDate) matchQuery.borrowDate.$gte = new Date(startDate);
    if (endDate) matchQuery.borrowDate.$lte = new Date(endDate);
  }
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalBorrowings: { $sum: 1 },
        activeBorrowings: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        returnedBorrowings: {
          $sum: { $cond: [{ $eq: ['$status', 'returned'] }, 1, 0] }
        },
        overdueBorrowings: {
          $sum: { $cond: [{ $eq: ['$status', 'overdue'] }, 1, 0] }
        },
        totalFines: { $sum: '$fine' },
        averageBorrowingDays: { $avg: '$borrowingDays' }
      }
    }
  ]);
};

const BorrowingRecord = mongoose.model('BorrowingRecord', borrowingRecordSchema);

module.exports = BorrowingRecord;
