/**
 * 后端服务器测试脚本
 * 
 * 测试后端服务器是否能正常启动
 */

const axios = require('axios');

async function testBackend() {
  console.log('🧪 测试后端服务器...');
  
  try {
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 测试健康检查端点
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ 健康检查通过:', healthResponse.data);
    
    // 测试根端点
    const rootResponse = await axios.get('http://localhost:3000/');
    console.log('✅ 根端点正常:', rootResponse.data);
    
    console.log('🎉 后端服务器测试通过！');
    
  } catch (error) {
    console.error('❌ 后端服务器测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testBackend();
}

module.exports = testBackend;
