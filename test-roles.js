/**
 * 角色权限测试脚本
 * 
 * 全面测试认证和授权中间件的功能
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// 测试用户数据
const testUsers = {
  admin: {
    username: 'testadmin',
    email: '<EMAIL>',
    password: 'Admin123456',
    firstName: 'Test',
    lastName: 'Admin'
  },
  member: {
    username: 'testmember',
    email: '<EMAIL>',
    password: 'Member123456',
    firstName: 'Test',
    lastName: 'Member'
  }
};

let tokens = {
  admin: '',
  member: ''
};

let userIds = {
  admin: '',
  member: ''
};

/**
 * 创建或登录测试用户
 */
async function setupTestUsers() {
  console.log('🔧 设置测试用户...\n');

  for (const [role, userData] of Object.entries(testUsers)) {
    try {
      console.log(`📝 注册${role}用户...`);
      const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
      tokens[role] = response.data.data.tokens.accessToken;
      userIds[role] = response.data.data.user._id;
      console.log(`✅ ${role}用户注册成功`);
      
    } catch (error) {
      if (error.response?.data?.error?.code === 'USER_ALREADY_EXISTS') {
        console.log(`ℹ️ ${role}用户已存在，尝试登录...`);
        try {
          const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
            email: userData.email,
            password: userData.password
          });
          tokens[role] = loginResponse.data.data.tokens.accessToken;
          userIds[role] = loginResponse.data.data.user._id;
          console.log(`✅ ${role}用户登录成功`);
        } catch (loginError) {
          console.error(`❌ ${role}用户登录失败:`, loginError.response?.data?.error?.message);
        }
      } else {
        console.error(`❌ 创建${role}用户失败:`, error.response?.data?.error?.message);
      }
    }
  }

  // TODO: 在实际应用中，需要手动设置admin用户的角色
  // 这里假设第一个注册的用户自动成为admin（仅用于测试）
  console.log('\n⚠️ 注意：需要手动将testadmin用户的角色设置为admin');
  console.log('可以通过数据库直接操作或管理界面完成');
}

/**
 * 测试公开路由
 */
async function testPublicRoute() {
  console.log('\n🧪 测试公开路由...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/test/public`);
    console.log('✅ 公开路由访问成功:', response.data.message);
  } catch (error) {
    console.error('❌ 公开路由访问失败:', error.response?.data?.error?.message);
  }
}

/**
 * 测试认证路由
 */
async function testAuthenticatedRoute() {
  console.log('\n🧪 测试认证路由...');
  
  // 测试无令牌访问
  try {
    await axios.get(`${API_BASE_URL}/test/authenticated`);
    console.log('❌ 无令牌访问应该被拒绝但却成功了');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ 无令牌访问正确被拒绝');
    }
  }
  
  // 测试有效令牌访问
  if (tokens.member) {
    try {
      const response = await axios.get(`${API_BASE_URL}/test/authenticated`, {
        headers: { Authorization: `Bearer ${tokens.member}` }
      });
      console.log('✅ 有效令牌访问成功:', response.data.data.user.username);
    } catch (error) {
      console.error('❌ 有效令牌访问失败:', error.response?.data?.error?.message);
    }
  }
}

/**
 * 测试管理员专用路由
 */
async function testAdminOnlyRoute() {
  console.log('\n🧪 测试管理员专用路由...');
  
  // 测试会员访问（应该被拒绝）
  if (tokens.member) {
    try {
      await axios.get(`${API_BASE_URL}/test/admin-only`, {
        headers: { Authorization: `Bearer ${tokens.member}` }
      });
      console.log('❌ 会员访问管理员路由应该被拒绝但却成功了');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ 会员访问管理员路由正确被拒绝');
      }
    }
  }
  
  // 测试管理员访问（应该成功）
  if (tokens.admin) {
    try {
      const response = await axios.get(`${API_BASE_URL}/test/admin-only`, {
        headers: { Authorization: `Bearer ${tokens.admin}` }
      });
      console.log('✅ 管理员访问成功:', response.data.message);
    } catch (error) {
      console.error('❌ 管理员访问失败:', error.response?.data?.error?.message);
      console.log('💡 提示：请确保testadmin用户的角色已设置为admin');
    }
  }
}

/**
 * 测试会员及以上路由
 */
async function testMemberAndAboveRoute() {
  console.log('\n🧪 测试会员及以上路由...');
  
  // 测试会员访问
  if (tokens.member) {
    try {
      const response = await axios.get(`${API_BASE_URL}/test/member-and-above`, {
        headers: { Authorization: `Bearer ${tokens.member}` }
      });
      console.log('✅ 会员访问成功:', response.data.data.user.username);
    } catch (error) {
      console.error('❌ 会员访问失败:', error.response?.data?.error?.message);
    }
  }
  
  // 测试管理员访问
  if (tokens.admin) {
    try {
      const response = await axios.get(`${API_BASE_URL}/test/member-and-above`, {
        headers: { Authorization: `Bearer ${tokens.admin}` }
      });
      console.log('✅ 管理员访问成功:', response.data.data.user.username);
    } catch (error) {
      console.error('❌ 管理员访问失败:', error.response?.data?.error?.message);
    }
  }
}

/**
 * 测试用户资源访问权限
 */
async function testUserResourceAccess() {
  console.log('\n🧪 测试用户资源访问权限...');
  
  if (tokens.member && userIds.member) {
    // 测试访问自己的资源
    try {
      const response = await axios.get(`${API_BASE_URL}/test/user/${userIds.member}/profile`, {
        headers: { Authorization: `Bearer ${tokens.member}` }
      });
      console.log('✅ 用户访问自己的资源成功');
    } catch (error) {
      console.error('❌ 用户访问自己的资源失败:', error.response?.data?.error?.message);
    }
    
    // 测试访问其他用户的资源（应该被拒绝）
    if (userIds.admin) {
      try {
        await axios.get(`${API_BASE_URL}/test/user/${userIds.admin}/profile`, {
          headers: { Authorization: `Bearer ${tokens.member}` }
        });
        console.log('❌ 用户访问他人资源应该被拒绝但却成功了');
      } catch (error) {
        if (error.response?.status === 403) {
          console.log('✅ 用户访问他人资源正确被拒绝');
        }
      }
    }
  }
}

/**
 * 测试复杂认证路由
 */
async function testComplexAuthRoute() {
  console.log('\n🧪 测试复杂认证路由...');
  
  if (tokens.member) {
    try {
      const response = await axios.get(`${API_BASE_URL}/test/complex-auth`, {
        headers: { Authorization: `Bearer ${tokens.member}` }
      });
      console.log('✅ 复杂认证路由访问成功:', response.data.data.user.username);
    } catch (error) {
      console.error('❌ 复杂认证路由访问失败:', error.response?.data?.error?.message);
    }
  }
}

/**
 * 获取测试路由信息
 */
async function getTestRouteInfo() {
  console.log('\n📋 获取测试路由信息...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/test/middleware-info`);
    console.log('✅ 测试路由信息获取成功');
    console.log('📝 可用的测试路由:');
    response.data.data.availableTestRoutes.forEach(route => {
      console.log(`   ${route.method} ${route.path} - ${route.description}`);
      console.log(`      要求: ${route.requirements}`);
    });
  } catch (error) {
    console.error('❌ 获取测试路由信息失败:', error.response?.data?.error?.message);
  }
}

/**
 * 运行所有角色权限测试
 */
async function runRoleTests() {
  console.log('🚀 开始角色权限测试\n');

  // 设置测试用户
  await setupTestUsers();

  // 运行各种测试
  await testPublicRoute();
  await testAuthenticatedRoute();
  await testAdminOnlyRoute();
  await testMemberAndAboveRoute();
  await testUserResourceAccess();
  await testComplexAuthRoute();
  await getTestRouteInfo();

  console.log('\n🎉 角色权限测试完成');
  console.log('\n📊 测试总结:');
  console.log(`- 管理员令牌: ${tokens.admin ? '✅ 已获取' : '❌ 未获取'}`);
  console.log(`- 会员令牌: ${tokens.member ? '✅ 已获取' : '❌ 未获取'}`);
  console.log(`- 管理员用户ID: ${userIds.admin || '未获取'}`);
  console.log(`- 会员用户ID: ${userIds.member || '未获取'}`);
  
  if (tokens.admin && tokens.member) {
    console.log('\n🔧 手动测试命令:');
    console.log(`curl -H "Authorization: Bearer ${tokens.admin}" ${API_BASE_URL}/test/admin-only`);
    console.log(`curl -H "Authorization: Bearer ${tokens.member}" ${API_BASE_URL}/test/member-and-above`);
  }
}

// 运行测试
if (require.main === module) {
  runRoleTests().catch(console.error);
}

module.exports = {
  setupTestUsers,
  testPublicRoute,
  testAuthenticatedRoute,
  testAdminOnlyRoute,
  testMemberAndAboveRoute,
  testUserResourceAccess,
  testComplexAuthRoute,
  runRoleTests,
  tokens: () => tokens,
  userIds: () => userIds
};
