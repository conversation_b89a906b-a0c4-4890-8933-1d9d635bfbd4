{"name": "library-management-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "图书管理系统前端应用", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vitest": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jsdom": "^23.0.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}