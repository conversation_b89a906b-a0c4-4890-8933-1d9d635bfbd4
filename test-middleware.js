/**
 * 中间件功能测试脚本
 * 
 * 测试认证和授权中间件的功能
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// 测试用户数据
const adminUser = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'Admin123456',
  firstName: 'Admin',
  lastName: 'User'
};

const memberUser = {
  username: 'member',
  email: '<EMAIL>',
  password: 'Member123456',
  firstName: 'Member',
  lastName: 'User'
};

let adminToken = '';
let memberToken = '';

/**
 * 创建测试用户
 */
async function createTestUsers() {
  console.log('🧪 创建测试用户...\n');

  try {
    // 注册管理员用户
    console.log('📝 注册管理员用户...');
    const adminResponse = await axios.post(`${API_BASE_URL}/auth/register`, adminUser);
    adminToken = adminResponse.data.data.tokens.accessToken;
    console.log('✅ 管理员用户创建成功');

    // 手动设置管理员角色（在实际应用中，这应该通过管理界面或数据库直接操作）
    // 这里我们假设有一个设置用户角色的API端点
    
  } catch (error) {
    if (error.response?.data?.error?.code === 'USER_ALREADY_EXISTS') {
      console.log('ℹ️ 管理员用户已存在，尝试登录...');
      try {
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: adminUser.email,
          password: adminUser.password
        });
        adminToken = loginResponse.data.data.tokens.accessToken;
        console.log('✅ 管理员用户登录成功');
      } catch (loginError) {
        console.error('❌ 管理员用户登录失败:', loginError.response?.data?.error?.message);
      }
    } else {
      console.error('❌ 创建管理员用户失败:', error.response?.data?.error?.message);
    }
  }

  try {
    // 注册普通会员用户
    console.log('📝 注册会员用户...');
    const memberResponse = await axios.post(`${API_BASE_URL}/auth/register`, memberUser);
    memberToken = memberResponse.data.data.tokens.accessToken;
    console.log('✅ 会员用户创建成功');
    
  } catch (error) {
    if (error.response?.data?.error?.code === 'USER_ALREADY_EXISTS') {
      console.log('ℹ️ 会员用户已存在，尝试登录...');
      try {
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: memberUser.email,
          password: memberUser.password
        });
        memberToken = loginResponse.data.data.tokens.accessToken;
        console.log('✅ 会员用户登录成功');
      } catch (loginError) {
        console.error('❌ 会员用户登录失败:', loginError.response?.data?.error?.message);
      }
    } else {
      console.error('❌ 创建会员用户失败:', error.response?.data?.error?.message);
    }
  }
}

/**
 * 测试无认证访问受保护路由
 */
async function testUnauthenticatedAccess() {
  console.log('\n🧪 测试无认证访问受保护路由...');

  try {
    await axios.get(`${API_BASE_URL}/auth/me`);
    console.log('❌ 应该被拒绝但却成功了');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ 无认证访问正确被拒绝:', error.response.data.error.message);
    } else {
      console.log('❓ 意外的错误:', error.response?.data?.error?.message);
    }
  }
}

/**
 * 测试无效令牌访问
 */
async function testInvalidTokenAccess() {
  console.log('\n🧪 测试无效令牌访问...');

  try {
    await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: {
        Authorization: 'Bearer invalid-token-here'
      }
    });
    console.log('❌ 应该被拒绝但却成功了');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ 无效令牌正确被拒绝:', error.response.data.error.message);
    } else {
      console.log('❓ 意外的错误:', error.response?.data?.error?.message);
    }
  }
}

/**
 * 测试有效令牌访问
 */
async function testValidTokenAccess() {
  console.log('\n🧪 测试有效令牌访问...');

  if (!memberToken) {
    console.log('❌ 没有有效的会员令牌');
    return;
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${memberToken}`
      }
    });
    console.log('✅ 有效令牌访问成功:', response.data.data.username);
  } catch (error) {
    console.log('❌ 有效令牌访问失败:', error.response?.data?.error?.message);
  }
}

/**
 * 创建一个测试路由来验证角色权限
 */
async function createTestRoutes() {
  // 注意：这个函数只是演示，实际的测试路由需要在服务器端创建
  console.log('\n📝 需要在服务器端创建以下测试路由来验证角色权限:');
  console.log('- GET /api/test/admin-only (只允许管理员)');
  console.log('- GET /api/test/member-and-above (允许会员和管理员)');
  console.log('- GET /api/test/public (公开访问)');
}

/**
 * 运行所有中间件测试
 */
async function runMiddlewareTests() {
  console.log('🚀 开始中间件功能测试\n');

  // 创建测试用户
  await createTestUsers();

  // 测试认证中间件
  await testUnauthenticatedAccess();
  await testInvalidTokenAccess();
  await testValidTokenAccess();

  // 提示创建测试路由
  await createTestRoutes();

  console.log('\n🎉 中间件功能测试完成');
  console.log('\n📋 测试总结:');
  console.log(`- 管理员令牌: ${adminToken ? '✅ 已获取' : '❌ 未获取'}`);
  console.log(`- 会员令牌: ${memberToken ? '✅ 已获取' : '❌ 未获取'}`);
}

// 运行测试
if (require.main === module) {
  runMiddlewareTests().catch(console.error);
}

module.exports = {
  createTestUsers,
  testUnauthenticatedAccess,
  testInvalidTokenAccess,
  testValidTokenAccess,
  runMiddlewareTests,
  adminToken: () => adminToken,
  memberToken: () => memberToken
};
