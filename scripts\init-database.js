/**
 * 数据库初始化脚本
 * 
 * 创建默认管理员账户、会员账户和示例数据
 */

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// 导入模型
const User = require('../src/models/User');
const Book = require('../src/models/Book');
const Category = require('../src/models/Category');

// 连接数据库
async function connectDB() {
  try {
    const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/library_management';
    await mongoose.connect(mongoURI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

// 创建默认用户
async function createDefaultUsers() {
  console.log('📝 创建默认用户账户...');

  try {
    // 检查是否已存在管理员
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('⚠️  管理员账户已存在，跳过创建');
    } else {
      // 创建管理员账户
      const adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin123456',
        role: 'admin',
        profile: {
          firstName: '系统',
          lastName: '管理员',
          phone: '13800138000',
          address: '系统管理中心'
        },
        isActive: true
      });

      await adminUser.save();
      console.log('✅ 管理员账户创建成功');
      console.log('   邮箱: <EMAIL>');
      console.log('   密码: Admin123456');
    }

    // 检查是否已存在会员
    const existingMember = await User.findOne({ email: '<EMAIL>' });
    if (existingMember) {
      console.log('⚠️  会员账户已存在，跳过创建');
    } else {
      // 创建会员账户
      const memberUser = new User({
        username: 'member',
        email: '<EMAIL>',
        password: 'Member123456',
        role: 'member',
        profile: {
          firstName: '张',
          lastName: '三',
          phone: '13900139000',
          address: '北京市朝阳区'
        },
        isActive: true
      });

      await memberUser.save();
      console.log('✅ 会员账户创建成功');
      console.log('   邮箱: <EMAIL>');
      console.log('   密码: Member123456');
    }

    // 创建更多测试用户
    const testUsers = [
      {
        username: 'user1',
        email: '<EMAIL>',
        password: 'User123456',
        role: 'member',
        profile: {
          firstName: '李',
          lastName: '四',
          phone: '13700137000'
        }
      },
      {
        username: 'user2',
        email: '<EMAIL>',
        password: 'User123456',
        role: 'member',
        profile: {
          firstName: '王',
          lastName: '五',
          phone: '13600136000'
        }
      }
    ];

    for (const userData of testUsers) {
      const existingUser = await User.findOne({ email: userData.email });
      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        console.log(`✅ 测试用户创建成功: ${userData.email}`);
      }
    }

  } catch (error) {
    console.error('❌ 创建用户失败:', error);
  }
}

// 创建图书分类
async function createCategories() {
  console.log('📚 创建图书分类...');

  const categories = [
    { name: '计算机科学', description: '编程、算法、数据结构等' },
    { name: '文学小说', description: '小说、散文、诗歌等' },
    { name: '历史传记', description: '历史、传记、回忆录等' },
    { name: '科学技术', description: '物理、化学、生物等' },
    { name: '经济管理', description: '经济学、管理学、商业等' },
    { name: '艺术设计', description: '美术、设计、音乐等' },
    { name: '教育心理', description: '教育学、心理学等' },
    { name: '医学健康', description: '医学、健康、养生等' }
  ];

  try {
    for (const categoryData of categories) {
      const existingCategory = await Category.findOne({ name: categoryData.name });
      if (!existingCategory) {
        const category = new Category(categoryData);
        await category.save();
        console.log(`✅ 分类创建成功: ${categoryData.name}`);
      }
    }
  } catch (error) {
    console.error('❌ 创建分类失败:', error);
  }
}

// 创建示例图书
async function createSampleBooks() {
  console.log('📖 创建示例图书...');

  try {
    // 获取管理员用户ID
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      console.log('⚠️  未找到管理员用户，跳过创建图书');
      return;
    }

    const sampleBooks = [
      {
        title: 'JavaScript高级程序设计',
        author: 'Nicholas C. Zakas',
        isbn: '9787115275790',
        description: '深入理解JavaScript语言核心特性的权威指南',
        category: '计算机科学',
        publisher: '人民邮电出版社',
        publishedYear: 2020,
        pages: 896,
        totalCopies: 5,
        stock: 5,
        tags: ['JavaScript', '前端开发', '编程'],
        createdBy: adminUser._id
      },
      {
        title: '算法导论',
        author: 'Thomas H. Cormen',
        isbn: '9787111407010',
        description: '计算机算法领域的经典教材',
        category: '计算机科学',
        publisher: '机械工业出版社',
        publishedYear: 2019,
        pages: 1312,
        totalCopies: 3,
        stock: 3,
        tags: ['算法', '数据结构', '计算机科学'],
        createdBy: adminUser._id
      },
      {
        title: '三体',
        author: '刘慈欣',
        isbn: '9787536692930',
        description: '科幻小说经典之作，雨果奖获奖作品',
        category: '文学小说',
        publisher: '重庆出版社',
        publishedYear: 2008,
        pages: 302,
        totalCopies: 8,
        stock: 8,
        tags: ['科幻', '小说', '雨果奖'],
        createdBy: adminUser._id
      },
      {
        title: '人类简史',
        author: '尤瓦尔·赫拉利',
        isbn: '9787508647357',
        description: '从动物到上帝的人类发展史',
        category: '历史传记',
        publisher: '中信出版社',
        publishedYear: 2014,
        pages: 440,
        totalCopies: 6,
        stock: 6,
        tags: ['历史', '人类学', '社会科学'],
        createdBy: adminUser._id
      },
      {
        title: 'React技术揭秘',
        author: '卡颂',
        isbn: '9787115563644',
        description: '深入React源码，理解React工作原理',
        category: '计算机科学',
        publisher: '人民邮电出版社',
        publishedYear: 2021,
        pages: 368,
        totalCopies: 4,
        stock: 4,
        tags: ['React', '前端框架', '源码分析'],
        createdBy: adminUser._id
      },
      {
        title: '设计模式',
        author: 'Erich Gamma',
        isbn: '9787111211945',
        description: '面向对象软件的可复用设计基础',
        category: '计算机科学',
        publisher: '机械工业出版社',
        publishedYear: 2007,
        pages: 254,
        totalCopies: 3,
        stock: 3,
        tags: ['设计模式', '面向对象', '软件工程'],
        createdBy: adminUser._id
      }
    ];

    for (const bookData of sampleBooks) {
      const existingBook = await Book.findOne({ isbn: bookData.isbn });
      if (!existingBook) {
        const book = new Book(bookData);
        await book.save();
        console.log(`✅ 图书创建成功: ${bookData.title}`);
      }
    }

  } catch (error) {
    console.error('❌ 创建图书失败:', error);
  }
}

// 主初始化函数
async function initializeDatabase() {
  console.log('🚀 开始初始化数据库...\n');

  try {
    await connectDB();
    
    await createDefaultUsers();
    console.log('');
    
    await createCategories();
    console.log('');
    
    await createSampleBooks();
    console.log('');

    console.log('🎉 数据库初始化完成！');
    console.log('\n📋 默认账户信息：');
    console.log('管理员账户：');
    console.log('  邮箱: <EMAIL>');
    console.log('  密码: Admin123456');
    console.log('');
    console.log('会员账户：');
    console.log('  邮箱: <EMAIL>');
    console.log('  密码: Member123456');
    console.log('');
    console.log('🌐 现在可以访问前端应用进行登录测试！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  } finally {
    await mongoose.connection.close();
    console.log('📝 数据库连接已关闭');
    process.exit(0);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeDatabase();
}

module.exports = initializeDatabase;
