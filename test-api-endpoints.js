/**
 * API端点测试脚本
 * 
 * 测试所有主要的API端点
 */

const http = require('http');

function testAPI(path, method = 'GET', headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3003,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: 10000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            data: data ? JSON.parse(data) : null,
            headers: res.headers
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function runAPITests() {
  console.log('🧪 开始API端点测试...\n');

  const tests = [
    { path: '/health', name: '健康检查' },
    { path: '/api/books/stats', name: '图书统计' },
    { path: '/api/books?limit=5', name: '图书列表' },
    { path: '/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5', name: '热门图书' },
    { path: '/api/borrowings/overdue', name: '逾期记录' },
    { path: '/api/borrowings?limit=10', name: '借阅记录' },
  ];

  for (const test of tests) {
    try {
      console.log(`测试: ${test.name} (${test.path})`);
      const result = await testAPI(test.path);
      
      if (result.status === 200) {
        console.log(`✅ 成功 - 状态码: ${result.status}`);
        if (result.data && result.data.success) {
          console.log(`   数据类型: ${typeof result.data.data}`);
          if (Array.isArray(result.data.data)) {
            console.log(`   数组长度: ${result.data.data.length}`);
          } else if (result.data.data && typeof result.data.data === 'object') {
            console.log(`   对象键: ${Object.keys(result.data.data).join(', ')}`);
          }
        }
      } else if (result.status === 401) {
        console.log(`🔒 需要认证 - 状态码: ${result.status}`);
      } else {
        console.log(`⚠️  状态码: ${result.status}`);
        if (result.data && result.data.error) {
          console.log(`   错误: ${result.data.error.message}`);
        }
      }
    } catch (error) {
      console.log(`❌ 失败: ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 API端点测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAPITests();
}

module.exports = runAPITests;
