/**
 * 借阅服务
 * 
 * 处理图书借阅相关的API调用
 */

import api from './api';

export const borrowingService = {
  /**
   * 借阅图书
   * @param {string} bookId - 图书ID
   * @returns {Promise} API响应
   */
  borrowBook: async (bookId) => {
    const response = await api.post(`/borrowings/borrow/${bookId}`);
    return response.data;
  },

  /**
   * 归还图书
   * @param {string} recordId - 借阅记录ID
   * @returns {Promise} API响应
   */
  returnBook: async (recordId) => {
    const response = await api.patch(`/borrowings/${recordId}/return`);
    return response.data;
  },

  /**
   * 续借图书
   * @param {string} recordId - 借阅记录ID
   * @returns {Promise} API响应
   */
  renewBook: async (recordId) => {
    const response = await api.patch(`/borrowings/${recordId}/renew`);
    return response.data;
  },

  /**
   * 获取个人借阅记录
   * @param {Object} params - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.limit=10] - 每页数量
   * @param {string} [params.status] - 状态筛选
   * @param {string} [params.sortBy] - 排序字段
   * @param {string} [params.sortOrder] - 排序顺序
   * @returns {Promise} API响应
   */
  getMyBorrowingRecords: async (params = {}) => {
    const response = await api.get('/borrowings/my-records', { params });
    return response.data;
  },

  /**
   * 获取当前借阅中的图书
   * @returns {Promise} API响应
   */
  getCurrentBorrowings: async () => {
    const response = await api.get('/borrowings/current');
    return response.data;
  },

  /**
   * 获取所有借阅记录（管理员权限）
   * @param {Object} params - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.limit=10] - 每页数量
   * @param {string} [params.userId] - 用户ID筛选
   * @param {string} [params.bookId] - 图书ID筛选
   * @param {string} [params.status] - 状态筛选
   * @param {string} [params.sortBy] - 排序字段
   * @param {string} [params.sortOrder] - 排序顺序
   * @returns {Promise} API响应
   */
  getAllBorrowingRecords: async (params = {}) => {
    const response = await api.get('/borrowings', { params });
    return response.data;
  },

  /**
   * 获取逾期借阅记录（管理员权限）
   * @returns {Promise} API响应
   */
  getOverdueRecords: async () => {
    const response = await api.get('/borrowings/overdue');
    return response.data;
  },

  /**
   * 获取借阅统计信息
   * @param {string} [startDate] - 开始日期
   * @param {string} [endDate] - 结束日期
   * @returns {Promise} API响应
   */
  getBorrowingStats: async (startDate, endDate) => {
    const params = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    
    const response = await api.get('/borrowings/stats', { params });
    return response.data;
  },

  /**
   * 检查图书是否可借阅
   * @param {string} bookId - 图书ID
   * @returns {Promise} API响应
   */
  checkBookAvailability: async (bookId) => {
    const response = await api.get(`/books/${bookId}`);
    const book = response.data.data;
    
    return {
      available: book.isAvailable,
      stock: book.stock,
      status: book.status,
    };
  },

  /**
   * 获取用户借阅历史
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getBorrowingHistory: async (params = {}) => {
    const response = await api.get('/borrowings/my-records', {
      params: {
        status: 'returned',
        ...params,
      },
    });
    return response.data;
  },

  /**
   * 获取当前活跃借阅
   * @returns {Promise} API响应
   */
  getActiveBorrowings: async () => {
    const response = await api.get('/borrowings/my-records', {
      params: {
        status: 'active',
      },
    });
    return response.data;
  },

  /**
   * 获取逾期借阅
   * @returns {Promise} API响应
   */
  getOverdueBorrowings: async () => {
    const response = await api.get('/borrowings/my-records', {
      params: {
        status: 'overdue',
      },
    });
    return response.data;
  },

  /**
   * 计算借阅费用（如果有逾期罚金）
   * @param {string} recordId - 借阅记录ID
   * @returns {Promise} API响应
   */
  calculateBorrowingFee: async (recordId) => {
    const response = await api.get(`/borrowings/${recordId}`);
    const record = response.data.data;
    
    // 计算逾期天数和罚金
    const now = new Date();
    const dueDate = new Date(record.dueDate);
    const isOverdue = now > dueDate && record.status === 'active';
    const overdueDays = isOverdue ? Math.ceil((now - dueDate) / (1000 * 60 * 60 * 24)) : 0;
    const finePerDay = parseFloat(import.meta.env.VITE_FINE_PER_DAY) || 1.0;
    const fine = overdueDays * finePerDay;
    
    return {
      isOverdue,
      overdueDays,
      fine,
      record,
    };
  },

  /**
   * 获取借阅记录详情
   * @param {string} recordId - 借阅记录ID
   * @returns {Promise} API响应
   */
  getBorrowingRecord: async (recordId) => {
    const response = await api.get(`/borrowings/${recordId}`);
    return response.data;
  },

  /**
   * 获取用户借阅统计
   * @param {string} userId - 用户ID
   * @returns {Promise} API响应
   */
  getUserBorrowingStats: async (userId) => {
    const response = await api.get(`/users/${userId}/borrowing-stats`);
    return response.data;
  },

  /**
   * 评价图书
   * @param {string} recordId - 借阅记录ID
   * @param {Object} ratingData - 评价数据
   * @returns {Promise} API响应
   */
  rateBook: async (recordId, ratingData) => {
    const response = await api.post(`/borrowings/${recordId}/rate`, ratingData);
    return response.data;
  },

  /**
   * 获取用户借阅统计
   * @param {string} userId - 用户ID
   * @returns {Promise} API响应
   */
  getUserBorrowingStats: async (userId) => {
    const response = await api.get(`/users/${userId}/borrowing-stats`);
    return response.data;
  },

  /**
   * 获取借阅统计（管理员）
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getBorrowingStats: async (params = {}) => {
    const response = await api.get('/borrowings/stats', { params });
    return response.data;
  },

  /**
   * 获取所有借阅记录（管理员）
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getAllBorrowingRecords: async (params = {}) => {
    const response = await api.get('/borrowings', { params });
    return response.data;
  },

  /**
   * 获取逾期记录
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getOverdueRecords: async (params = {}) => {
    const response = await api.get('/borrowings/overdue', { params });
    return response.data;
  },

  /**
   * 更新借阅记录（管理员）
   * @param {string} recordId - 记录ID
   * @param {Object} data - 更新数据
   * @returns {Promise} API响应
   */
  updateBorrowingRecord: async (recordId, data) => {
    const response = await api.put(`/borrowings/${recordId}/admin`, data);
    return response.data;
  },
};
