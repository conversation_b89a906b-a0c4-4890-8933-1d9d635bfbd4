/**
 * MongoDB数据库连接配置
 * 
 * 使用Mongoose连接MongoDB数据库
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');

/**
 * 连接MongoDB数据库
 * @returns {Promise} 数据库连接Promise
 */
const connectDB = async () => {
  try {
    // 获取数据库连接字符串
    const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/library_management';
    
    // Mongoose连接选项
    const options = {
      // 使用新的URL解析器
      useNewUrlParser: true,
      useUnifiedTopology: true,
      
      // 连接池配置
      maxPoolSize: 10, // 最大连接数
      serverSelectionTimeoutMS: 5000, // 服务器选择超时
      socketTimeoutMS: 45000, // Socket超时
      
      // 缓冲配置
      bufferMaxEntries: 0,
      bufferCommands: false,
      
      // 其他选项
      family: 4 // 使用IPv4
    };

    // 连接数据库
    const conn = await mongoose.connect(mongoURI, options);
    
    logger.info(`MongoDB连接成功: ${conn.connection.host}:${conn.connection.port}/${conn.connection.name}`);
    
    return conn;
    
  } catch (error) {
    logger.error('MongoDB连接失败:', error);
    throw error;
  }
};

/**
 * 断开数据库连接
 * @returns {Promise} 断开连接Promise
 */
const disconnectDB = async () => {
  try {
    await mongoose.connection.close();
    logger.info('MongoDB连接已断开');
  } catch (error) {
    logger.error('断开MongoDB连接时出错:', error);
    throw error;
  }
};

// 监听连接事件
mongoose.connection.on('connected', () => {
  logger.info('Mongoose连接已建立');
});

mongoose.connection.on('error', (error) => {
  logger.error('Mongoose连接错误:', error);
});

mongoose.connection.on('disconnected', () => {
  logger.warn('Mongoose连接已断开');
});

// 应用终止时关闭数据库连接
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  logger.info('应用终止，MongoDB连接已关闭');
  process.exit(0);
});

module.exports = connectDB;
