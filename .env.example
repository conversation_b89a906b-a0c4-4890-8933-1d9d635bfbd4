# 服务器配置
NODE_ENV=development
PORT=3000

# 数据库配置
MONGO_URI=mongodb://localhost:27017/library_management

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here
JWT_REFRESH_EXPIRE=30d

# 前端URL（用于CORS配置）
FRONTEND_URL=http://localhost:5173

# 邮件配置（用于通知功能）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 日志配置
LOG_LEVEL=info

# 文件上传配置
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# 系统配置
DEFAULT_BORROWING_PERIOD=30
MAX_RENEWALS=2
FINE_PER_DAY=1.0
MAX_BOOKS_PER_USER=5

# Redis配置（可选，用于缓存和会话）
REDIS_URL=redis://localhost:6379

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
