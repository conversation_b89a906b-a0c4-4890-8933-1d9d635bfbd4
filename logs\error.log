{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:08","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:25","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: 邮箱或密码错误","method":"POST","service":"library-management-api","stack":"Error: 邮箱或密码错误\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\auth.controller.js:84:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 22:59:43","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: 令牌无效，用户不存在","method":"GET","service":"library-management-api","stack":"Error: 令牌无效，用户不存在\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:39:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:07:10","url":"/api/borrowings/current","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:26","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:27","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:31","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: language override unsupported: Chinese","method":"POST","service":"library-management-api","stack":"MongoServerError: language override unsupported: Chinese\n    at InsertOneOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-06-21 23:12:32","url":"/api/books","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:17:59","url":"/api/borrowings/borrow/6856cd08582fbdb2d487e435","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:18:00","url":"/api/borrowings/borrow/6856cd08582fbdb2d487e435","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:20:37","url":"/api/borrowings/borrow/6856cda3f287fdb5dd33552d","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:21:08","url":"/api/borrowings/borrow/6856cda3f287fdb5dd33552d","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: Transaction numbers are only allowed on a replica set member or mongos","method":"POST","service":"library-management-api","stack":"MongoServerError: Transaction numbers are only allowed on a replica set member or mongos\n    at Connection.sendCommand (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:305:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cmap\\connection.js:333:26)\n    at async Server.command (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\sdam\\server.js:171:29)\n    at async FindOperation.execute (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\find.js:36:16)\n    at async tryOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async FindCursor._initialize (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\find_cursor.js:61:26)\n    at async FindCursor.cursorInit (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:633:27)\n    at async FindCursor.fetchBatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\mongodb\\lib\\cursor\\abstract_cursor.js:667:13)","timestamp":"2025-06-21 23:21:12","url":"/api/borrowings/borrow/6856cda3f287fdb5dd33552d","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:32:59"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:33:31"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:34:57"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:35:09"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:43:57"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:45:32"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:46:26"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:46:58"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/books/stats"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/books?limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/borrowings/overdue"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:15","url":"/api/borrowings?limit=10"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/books/stats"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/books?limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/borrowings/overdue"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:23","url":"/api/borrowings?limit=10"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/books/stats"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/books?limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/borrowings/overdue"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:32","url":"/api/borrowings?limit=10"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/books/stats"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/books?limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/borrowings/overdue"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:54:53","url":"/api/borrowings?limit=10"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:55:15"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"未捕获的异常: listen EADDRINUSE: address already in use :::3002","port":3002,"service":"library-management-api","stack":"Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1907:16)\n    at listenInCluster (node:net:1964:12)\n    at Server.listen (node:net:2066:7)\n    at Function.listen (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\server.js:31:24)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-21 23:55:51"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/books/stats"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/books?limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/books?sortBy=borrowingStats.totalBorrowed&sortOrder=desc&limit=5"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/borrowings/overdue"}
{"ip":"::1","level":"error","message":"错误: 访问被拒绝，请提供有效的访问令牌","method":"GET","service":"library-management-api","stack":"Error: 访问被拒绝，请提供有效的访问令牌\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\auth.middleware.js:28:11\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\middlewares\\error.middleware.js:100:19\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-21 23:57:49","url":"/api/borrowings?limit=10"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:45","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:46","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:53","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"错误: borrowingRecord.renewBook is not a function","method":"PATCH","service":"library-management-api","stack":"TypeError: borrowingRecord.renewBook is not a function\n    at D:\\study\\college\\junior2\\SystemAnalysisAndDesign\\system\\src\\controllers\\borrowing.controller.js:209:27\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-21 23:58:54","url":"/api/borrowings/6856d5dba7bf214df6ac4099/renew","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
