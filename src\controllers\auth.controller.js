/**
 * 认证控制器 (Authentication Controller)
 * 
 * 处理用户注册、登录、登出等认证相关功能
 */

const { User } = require('../models');
const { generateTokenPair, verifyRefreshToken } = require('../config/jwt');
const { successResponse, errorResponse } = require('../utils/response');
const { asyncHandler, AppError } = require('../middlewares/error.middleware');
const logger = require('../utils/logger');

/**
 * 用户注册
 * @route POST /api/auth/register
 * @access Public
 */
const register = asyncHandler(async (req, res) => {
  const { username, email, password, firstName, lastName, phone } = req.body;

  // 检查必填字段
  if (!username || !email || !password) {
    throw new AppError('用户名、邮箱和密码是必需的', 400, 'MISSING_REQUIRED_FIELDS');
  }

  // 检查用户是否已存在
  const existingUser = await User.findOne({
    $or: [{ email }, { username }]
  });

  if (existingUser) {
    const field = existingUser.email === email ? '邮箱' : '用户名';
    throw new AppError(`该${field}已被注册`, 409, 'USER_ALREADY_EXISTS');
  }

  // 创建新用户
  const userData = {
    username,
    email,
    password,
    profile: {}
  };

  // 添加可选的个人信息
  if (firstName) userData.profile.firstName = firstName;
  if (lastName) userData.profile.lastName = lastName;
  if (phone) userData.profile.phone = phone;

  const user = await User.create(userData);

  // 生成JWT令牌
  const tokens = generateTokenPair(user);

  // 记录注册日志
  logger.info(`新用户注册: ${user.email} (${user.username})`);

  // 返回成功响应（不包含密码）
  const userResponse = user.toJSON();
  delete userResponse.password;

  res.status(201).json(successResponse({
    user: userResponse,
    tokens
  }, '注册成功'));
});

/**
 * 用户登录
 * @route POST /api/auth/login
 * @access Public
 */
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // 检查必填字段
  if (!email || !password) {
    throw new AppError('邮箱和密码是必需的', 400, 'MISSING_CREDENTIALS');
  }

  // 查找用户（包含密码字段）
  const user = await User.findByEmailWithPassword(email);

  if (!user) {
    throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS');
  }

  // 检查账户是否激活
  if (!user.isActive) {
    throw new AppError('账户已被禁用，请联系管理员', 403, 'ACCOUNT_DISABLED');
  }

  // 验证密码
  const isPasswordValid = await user.comparePassword(password);

  if (!isPasswordValid) {
    throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS');
  }

  // 更新最后登录时间
  await user.updateLastLogin();

  // 生成JWT令牌
  const tokens = generateTokenPair(user);

  // 记录登录日志
  logger.info(`用户登录: ${user.email} (${user.username})`);

  // 返回成功响应（不包含密码）
  const userResponse = user.toJSON();
  delete userResponse.password;

  res.json(successResponse({
    user: userResponse,
    tokens
  }, '登录成功'));
});

/**
 * 用户登出
 * @route POST /api/auth/logout
 * @access Private
 */
const logout = asyncHandler(async (req, res) => {
  // 在实际应用中，可以将令牌加入黑名单
  // 这里简单返回成功响应
  logger.info(`用户登出: ${req.user.email}`);

  res.json(successResponse(null, '登出成功'));
});

/**
 * 刷新访问令牌
 * @route POST /api/auth/refresh
 * @access Public
 */
const refreshToken = asyncHandler(async (req, res) => {
  const { refreshToken: token } = req.body;

  if (!token) {
    throw new AppError('刷新令牌是必需的', 400, 'MISSING_REFRESH_TOKEN');
  }

  try {
    // 验证刷新令牌
    const decoded = verifyRefreshToken(token);

    // 查找用户
    const user = await User.findById(decoded.userId);

    if (!user || !user.isActive) {
      throw new AppError('用户不存在或已被禁用', 401, 'INVALID_USER');
    }

    // 生成新的令牌对
    const tokens = generateTokenPair(user);

    res.json(successResponse({
      tokens
    }, '令牌刷新成功'));

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      throw new AppError('无效的刷新令牌', 401, 'INVALID_REFRESH_TOKEN');
    }
    throw error;
  }
});

/**
 * 忘记密码
 * @route POST /api/auth/forgot-password
 * @access Public
 */
const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    throw new AppError('邮箱是必需的', 400, 'MISSING_EMAIL');
  }

  const user = await User.findOne({ email });

  if (!user) {
    // 为了安全，即使用户不存在也返回成功消息
    return res.json(successResponse(null, '如果该邮箱存在，重置密码链接已发送'));
  }

  // TODO: 实现发送重置密码邮件的逻辑
  // 这里应该生成重置令牌并发送邮件

  logger.info(`密码重置请求: ${email}`);

  res.json(successResponse(null, '如果该邮箱存在，重置密码链接已发送'));
});

/**
 * 重置密码
 * @route POST /api/auth/reset-password
 * @access Public
 */
const resetPassword = asyncHandler(async (req, res) => {
  const { token, newPassword } = req.body;

  if (!token || !newPassword) {
    throw new AppError('重置令牌和新密码是必需的', 400, 'MISSING_REQUIRED_FIELDS');
  }

  // TODO: 实现验证重置令牌和更新密码的逻辑
  // 这里应该验证重置令牌并更新用户密码

  res.json(successResponse(null, '密码重置成功'));
});

/**
 * 获取当前用户信息
 * @route GET /api/auth/me
 * @access Private
 */
const getCurrentUser = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user.userId)
    .populate('borrowingStats');

  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  res.json(successResponse(user, '获取用户信息成功'));
});

module.exports = {
  register,
  login,
  logout,
  refreshToken,
  forgotPassword,
  resetPassword,
  getCurrentUser
};
