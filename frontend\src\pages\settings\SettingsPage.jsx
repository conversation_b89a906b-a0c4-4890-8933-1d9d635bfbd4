import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  Divider,
  Typography,
  Space,
  message,
  Row,
  Col,
  Avatar,
  Upload,
  Tabs,
} from 'antd';
import {
  SettingOutlined,
  UserOutlined,
  LockOutlined,
  BellOutlined,
  EyeOutlined,
  UploadOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../context/AuthContext';
import { useMutation, useQueryClient } from 'react-query';
import { userService } from '../../services/userService';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

function SettingsPage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('profile');

  // 更新个人资料
  const updateProfileMutation = useMutation({
    mutationFn: (data) => userService.updateProfile(user.userId, data),
    onSuccess: () => {
      message.success('个人资料更新成功');
      queryClient.invalidateQueries(['user', user.userId]);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || '更新失败');
    },
  });

  // 修改密码
  const changePasswordMutation = useMutation({
    mutationFn: (data) => userService.changePassword(user.userId, data),
    onSuccess: () => {
      message.success('密码修改成功');
      passwordForm.resetFields();
    },
    onError: (error) => {
      message.error(error.response?.data?.message || '密码修改失败');
    },
  });

  // 处理个人资料提交
  const handleProfileSubmit = (values) => {
    updateProfileMutation.mutate(values);
  };

  // 处理密码修改提交
  const handlePasswordSubmit = (values) => {
    changePasswordMutation.mutate(values);
  };

  // 处理通知设置提交
  const handleNotificationSubmit = (values) => {
    message.success('通知设置已保存');
    console.log('Notification settings:', values);
  };

  // 头像上传配置
  const uploadProps = {
    name: 'avatar',
    action: '/api/upload/avatar',
    headers: {
      authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片!');
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!');
      }
      return isJpgOrPng && isLt2M;
    },
    onChange: (info) => {
      if (info.file.status === 'done') {
        message.success('头像上传成功');
      } else if (info.file.status === 'error') {
        message.error('头像上传失败');
      }
    },
  };

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2}>
          <SettingOutlined className="mr-2" />
          系统设置
        </Title>
      </div>

      <Row gutter={24}>
        <Col span={24}>
          <Card>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              {/* 个人资料 */}
              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    个人资料
                  </span>
                }
                key="profile"
              >
                <div className="max-w-2xl">
                  <div className="mb-6 text-center">
                    <Avatar size={80} icon={<UserOutlined />} className="mb-4" />
                    <div>
                      <Upload {...uploadProps} showUploadList={false}>
                        <Button icon={<UploadOutlined />}>更换头像</Button>
                      </Upload>
                    </div>
                  </div>

                  <Form
                    form={profileForm}
                    layout="vertical"
                    onFinish={handleProfileSubmit}
                    initialValues={{
                      username: user?.username,
                      email: user?.email,
                      firstName: user?.profile?.firstName,
                      lastName: user?.profile?.lastName,
                      phone: user?.profile?.phone,
                      address: user?.profile?.address,
                    }}
                  >
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="firstName"
                          label="姓"
                          rules={[{ required: true, message: '请输入姓' }]}
                        >
                          <Input placeholder="请输入姓" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="lastName"
                          label="名"
                          rules={[{ required: true, message: '请输入名' }]}
                        >
                          <Input placeholder="请输入名" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="username"
                      label="用户名"
                      rules={[{ required: true, message: '请输入用户名' }]}
                    >
                      <Input placeholder="请输入用户名" />
                    </Form.Item>

                    <Form.Item
                      name="email"
                      label="邮箱"
                      rules={[
                        { required: true, message: '请输入邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' },
                      ]}
                    >
                      <Input placeholder="请输入邮箱" disabled />
                    </Form.Item>

                    <Form.Item
                      name="phone"
                      label="手机号"
                      rules={[
                        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
                      ]}
                    >
                      <Input placeholder="请输入手机号" />
                    </Form.Item>

                    <Form.Item name="address" label="地址">
                      <Input.TextArea rows={3} placeholder="请输入地址" />
                    </Form.Item>

                    <Form.Item>
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SaveOutlined />}
                        loading={updateProfileMutation.isLoading}
                      >
                        保存资料
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </TabPane>

              {/* 安全设置 */}
              <TabPane
                tab={
                  <span>
                    <LockOutlined />
                    安全设置
                  </span>
                }
                key="security"
              >
                <div className="max-w-md">
                  <Title level={4}>修改密码</Title>
                  <Text type="secondary" className="block mb-4">
                    为了账户安全，建议定期更换密码
                  </Text>

                  <Form
                    form={passwordForm}
                    layout="vertical"
                    onFinish={handlePasswordSubmit}
                  >
                    <Form.Item
                      name="currentPassword"
                      label="当前密码"
                      rules={[{ required: true, message: '请输入当前密码' }]}
                    >
                      <Input.Password placeholder="请输入当前密码" />
                    </Form.Item>

                    <Form.Item
                      name="newPassword"
                      label="新密码"
                      rules={[
                        { required: true, message: '请输入新密码' },
                        { min: 6, message: '密码至少6位' },
                      ]}
                    >
                      <Input.Password placeholder="请输入新密码" />
                    </Form.Item>

                    <Form.Item
                      name="confirmPassword"
                      label="确认新密码"
                      dependencies={['newPassword']}
                      rules={[
                        { required: true, message: '请确认新密码' },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (!value || getFieldValue('newPassword') === value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error('两次输入的密码不一致'));
                          },
                        }),
                      ]}
                    >
                      <Input.Password placeholder="请再次输入新密码" />
                    </Form.Item>

                    <Form.Item>
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SaveOutlined />}
                        loading={changePasswordMutation.isLoading}
                      >
                        修改密码
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </TabPane>

              {/* 通知设置 */}
              <TabPane
                tab={
                  <span>
                    <BellOutlined />
                    通知设置
                  </span>
                }
                key="notifications"
              >
                <div className="max-w-md">
                  <Form
                    form={notificationForm}
                    layout="vertical"
                    onFinish={handleNotificationSubmit}
                    initialValues={{
                      emailNotifications: true,
                      borrowingReminders: true,
                      overdueNotifications: true,
                      systemUpdates: false,
                    }}
                  >
                    <Form.Item name="emailNotifications" valuePropName="checked">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">邮件通知</div>
                          <div className="text-gray-500 text-sm">接收系统邮件通知</div>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="borrowingReminders" valuePropName="checked">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">借阅提醒</div>
                          <div className="text-gray-500 text-sm">图书到期前提醒</div>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="overdueNotifications" valuePropName="checked">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">逾期通知</div>
                          <div className="text-gray-500 text-sm">图书逾期时通知</div>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Divider />

                    <Form.Item name="systemUpdates" valuePropName="checked">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">系统更新</div>
                          <div className="text-gray-500 text-sm">接收系统更新通知</div>
                        </div>
                        <Switch />
                      </div>
                    </Form.Item>

                    <Form.Item className="mt-6">
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SaveOutlined />}
                      >
                        保存设置
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default SettingsPage;
