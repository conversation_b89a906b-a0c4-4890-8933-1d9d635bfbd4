/**
 * Express应用配置文件
 * 
 * 功能：
 * 1. 配置Express应用
 * 2. 设置中间件
 * 3. 配置路由
 * 4. 错误处理
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const logger = require('./utils/logger');
const { successResponse, errorResponse } = require('./utils/response');
const errorHandler = require('./middlewares/error.middleware');

// 创建Express应用
const app = express();

// 信任代理（如果使用反向代理）
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS配置
const corsOptions = {
  origin: function (origin, callback) {
    // 允许的域名列表
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:4173',
      process.env.FRONTEND_URL
    ].filter(Boolean);

    // 开发环境允许所有来源
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }

    // 生产环境检查来源
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('不被CORS策略允许的来源'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// 请求压缩
app.use(compression());

// 请求体解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// API请求频率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: '请求过于频繁，请稍后再试'
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json(successResponse({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  }, '服务运行正常'));
});

// 根路由 - Hello World
app.get('/', (req, res) => {
  res.json(successResponse({
    name: '图书管理系统API',
    version: '1.0.0',
    description: '基于Node.js和Express的图书管理系统后端API',
    documentation: '/api/docs',
    health: '/health'
  }, '欢迎使用图书管理系统API'));
});

// API路由
const authRoutes = require('./routes/auth.routes');
const bookRoutes = require('./routes/book.routes');
const borrowingRoutes = require('./routes/borrowing.routes');

app.use('/api/auth', authRoutes);
app.use('/api/books', bookRoutes);
app.use('/api/borrowings', borrowingRoutes);

// 测试路由（仅在开发环境中启用）
if (process.env.NODE_ENV === 'development') {
  const testRoutes = require('./routes/test.routes');
  app.use('/api/test', testRoutes);
}

// 404处理
app.use('*', (req, res) => {
  res.status(404).json(errorResponse(
    'NOT_FOUND',
    `路由 ${req.originalUrl} 不存在`,
    404
  ));
});

// 全局错误处理中间件
app.use(errorHandler);

module.exports = app;
