# 图书管理系统后端API

基于Node.js、Express和MongoDB的图书管理系统后端API服务。

## 功能特性

- 🔐 用户认证与授权（JWT）
- 📚 图书管理（增删改查）
- 📖 借阅管理（借书、还书、续借）
- 👥 用户角色管理（管理员、会员）
- 🔍 图书搜索与分类
- 📊 统计报表
- 🔔 通知系统
- 🛡️ 安全防护（Helmet、CORS、Rate Limiting）
- 📝 日志记录
- ✅ 数据验证

## 技术栈

- **运行环境**: Node.js 16+
- **Web框架**: Express.js
- **数据库**: MongoDB
- **ODM**: Mongoose
- **认证**: JWT (JSON Web Token)
- **密码加密**: bcryptjs
- **日志**: Winston
- **测试**: Jest + Supertest
- **代码规范**: ESLint + Prettier

## 快速开始

### 1. 环境要求

- Node.js >= 16.0.0
- MongoDB >= 4.4
- npm >= 8.0.0

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd library-management-backend

# 安装依赖
npm install
```

### 3. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
nano .env
```

### 4. 启动服务

```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 5. 验证服务

访问 http://localhost:3000 查看API欢迎页面
访问 http://localhost:3000/health 查看服务健康状态

## 项目结构

```
library-management-backend/
├── src/
│   ├── config/          # 配置文件
│   ├── controllers/     # 控制器
│   ├── models/          # 数据模型
│   ├── routes/          # 路由
│   ├── middlewares/     # 中间件
│   ├── services/        # 服务层
│   ├── utils/           # 工具函数
│   └── app.js           # Express应用配置
├── tests/               # 测试文件
├── logs/                # 日志文件
├── docs/                # 文档
├── .env.example         # 环境变量示例
├── package.json         # 项目配置
├── server.js            # 服务器启动文件
└── README.md            # 项目说明
```

## API文档

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

### 图书管理
- `GET /api/books` - 获取图书列表
- `POST /api/books` - 添加图书（管理员）
- `GET /api/books/:id` - 获取图书详情
- `PUT /api/books/:id` - 更新图书（管理员）
- `DELETE /api/books/:id` - 删除图书（管理员）

### 借阅管理
- `POST /api/borrowings/borrow/:bookId` - 借阅图书
- `PATCH /api/borrowings/:recordId/return` - 归还图书
- `GET /api/borrowings/my-records` - 获取个人借阅记录

详细API文档请参考 `/docs/api.md`

## 开发指南

### 运行测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

### 代码规范

```bash
# 检查代码规范
npm run lint

# 自动修复代码规范问题
npm run lint:fix
```

### 数据库操作

```bash
# 连接MongoDB
mongo mongodb://localhost:27017/library_management

# 查看集合
show collections

# 查看用户数据
db.users.find().pretty()
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t library-management-api .

# 运行容器
docker run -p 3000:3000 --env-file .env library-management-api
```

### PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start server.js --name "library-api"

# 查看状态
pm2 status

# 查看日志
pm2 logs library-api
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/library-management-backend]
