/**
 * 用户管理页面（管理员）
 * 
 * 管理员专用的用户管理界面
 */

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Typography,
  Tag,
  Tooltip,
  Avatar,
  Statistic,
  Descriptions,
} from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  TeamOutlined,
  CrownOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { userService } from '../../services/userService';
import toast from 'react-hot-toast';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

function UserManagePage() {
  const [searchParams, setSearchParams] = useState({
    search: '',
    role: 'all',
    status: 'all',
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const queryClient = useQueryClient();

  // 获取用户列表
  const {
    data: usersData,
    isLoading,
    refetch,
  } = useQuery(
    ['adminUsers', searchParams],
    () => userService.getAllUsers(searchParams),
    {
      keepPreviousData: true,
    }
  );

  // 更新用户状态
  const updateUserStatusMutation = useMutation(
    ({ userId, isActive }) => userService.updateUserStatus(userId, isActive),
    {
      onSuccess: () => {
        toast.success('用户状态更新成功！');
        refetch();
      },
      onError: (error) => {
        toast.error(error.response?.data?.error?.message || '更新失败');
      },
    }
  );

  // 更新用户角色
  const updateUserRoleMutation = useMutation(
    ({ userId, role }) => userService.updateUserRole(userId, role),
    {
      onSuccess: () => {
        toast.success('用户角色更新成功！');
        refetch();
      },
      onError: (error) => {
        toast.error(error.response?.data?.error?.message || '更新失败');
      },
    }
  );

  // 处理搜索
  const handleSearch = (value) => {
    setSearchParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // 处理筛选
  const handleFilterChange = (key, value) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  // 处理分页
  const handleTableChange = (pagination, filters, sorter) => {
    setSearchParams(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: sorter.field || 'createdAt',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    }));
  };

  // 查看用户详情
  const handleViewUser = (user) => {
    setSelectedUser(user);
    setDetailModalVisible(true);
  };

  // 切换用户状态
  const handleToggleUserStatus = (userId, currentStatus) => {
    updateUserStatusMutation.mutate({
      userId,
      isActive: !currentStatus,
    });
  };

  // 更改用户角色
  const handleChangeUserRole = (userId, newRole) => {
    updateUserRoleMutation.mutate({
      userId,
      role: newRole,
    });
  };

  // 获取角色标签
  const getRoleTag = (role) => {
    const roleConfig = {
      admin: { color: 'red', icon: <CrownOutlined />, text: '管理员' },
      member: { color: 'blue', icon: <UserOutlined />, text: '会员' },
    };
    
    const config = roleConfig[role] || roleConfig.member;
    
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 获取状态标签
  const getStatusTag = (isActive) => {
    return (
      <Tag color={isActive ? 'green' : 'red'}>
        {isActive ? '活跃' : '禁用'}
      </Tag>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <Avatar
            size={40}
            src={record.profile?.avatar}
            icon={<UserOutlined />}
          />
          <div>
            <div className="font-medium">{record.username}</div>
            <div className="text-gray-500 text-sm">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: '姓名',
      key: 'fullName',
      render: (_, record) => (
        <div>
          {record.profile?.firstName && record.profile?.lastName
            ? `${record.profile.lastName}${record.profile.firstName}`
            : '-'
          }
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => getRoleTag(role),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => getStatusTag(isActive),
    },
    {
      title: '借阅统计',
      key: 'borrowingStats',
      render: (_, record) => (
        <div className="text-sm">
          <div>当前借阅: {record.borrowingStats?.currentBorrowed || 0}</div>
          <div>总借阅: {record.borrowingStats?.totalBorrowed || 0}</div>
        </div>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      render: (date) => date ? new Date(date).toLocaleDateString() : '-',
      sorter: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewUser(record)}
            />
          </Tooltip>
          
          <Tooltip title={record.isActive ? '禁用用户' : '启用用户'}>
            <Button
              type="text"
              icon={record.isActive ? <LockOutlined /> : <UnlockOutlined />}
              onClick={() => handleToggleUserStatus(record._id, record.isActive)}
              loading={updateUserStatusMutation.isLoading}
            />
          </Tooltip>
          
          <Select
            size="small"
            value={record.role}
            onChange={(value) => handleChangeUserRole(record._id, value)}
            loading={updateUserRoleMutation.isLoading}
            style={{ width: 80 }}
          >
            <Option value="member">会员</Option>
            <Option value="admin">管理员</Option>
          </Select>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2}>
          <TeamOutlined className="mr-2" />
          用户管理
        </Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={usersData?.data?.pagination?.totalItems || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={usersData?.data?.items?.filter(user => user.isActive).length || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="管理员"
              value={usersData?.data?.items?.filter(user => user.role === 'admin').length || 0}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="会员"
              value={usersData?.data?.items?.filter(user => user.role === 'member').length || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="搜索用户名或邮箱"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
            />
          </Col>
          <Col xs={12} sm={4}>
            <Select
              placeholder="选择角色"
              value={searchParams.role}
              onChange={(value) => handleFilterChange('role', value)}
              className="w-full"
            >
              <Option value="all">所有角色</Option>
              <Option value="admin">管理员</Option>
              <Option value="member">会员</Option>
            </Select>
          </Col>
          <Col xs={12} sm={4}>
            <Select
              placeholder="选择状态"
              value={searchParams.status}
              onChange={(value) => handleFilterChange('status', value)}
              className="w-full"
            >
              <Option value="all">所有状态</Option>
              <Option value="active">活跃</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => refetch()}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 用户表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={usersData?.data?.items || []}
          loading={isLoading}
          rowKey="_id"
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.limit,
            total: usersData?.data?.pagination?.totalItems || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 用户详情模态框 */}
      <Modal
        title="用户详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedUser(null);
        }}
        footer={null}
        width={600}
      >
        {selectedUser && (
          <div>
            <div className="text-center mb-6">
              <Avatar
                size={80}
                src={selectedUser.profile?.avatar}
                icon={<UserOutlined />}
              />
              <div className="mt-2">
                <Title level={4}>{selectedUser.username}</Title>
                <div className="text-gray-500">{selectedUser.email}</div>
              </div>
            </div>

            <Descriptions bordered column={2}>
              <Descriptions.Item label="用户名">
                {selectedUser.username}
              </Descriptions.Item>
              <Descriptions.Item label="邮箱">
                {selectedUser.email}
              </Descriptions.Item>
              <Descriptions.Item label="姓名">
                {selectedUser.profile?.firstName && selectedUser.profile?.lastName
                  ? `${selectedUser.profile.lastName}${selectedUser.profile.firstName}`
                  : '-'
                }
              </Descriptions.Item>
              <Descriptions.Item label="电话">
                {selectedUser.profile?.phone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="角色">
                {getRoleTag(selectedUser.role)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedUser.isActive)}
              </Descriptions.Item>
              <Descriptions.Item label="当前借阅">
                {selectedUser.borrowingStats?.currentBorrowed || 0} 本
              </Descriptions.Item>
              <Descriptions.Item label="总借阅">
                {selectedUser.borrowingStats?.totalBorrowed || 0} 本
              </Descriptions.Item>
              <Descriptions.Item label="逾期罚金">
                ¥{selectedUser.borrowingStats?.overdueFines || 0}
              </Descriptions.Item>
              <Descriptions.Item label="注册时间">
                {new Date(selectedUser.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="最后登录">
                {selectedUser.lastLoginAt 
                  ? new Date(selectedUser.lastLoginAt).toLocaleString()
                  : '从未登录'
                }
              </Descriptions.Item>
              <Descriptions.Item label="地址" span={2}>
                {selectedUser.profile?.address || '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default UserManagePage;
