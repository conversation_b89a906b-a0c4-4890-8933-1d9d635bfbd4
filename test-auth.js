/**
 * 认证功能测试脚本
 * 
 * 用于测试用户注册和登录功能
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// 测试数据
const testUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'Test123456',
  firstName: 'Test',
  lastName: 'User'
};

/**
 * 测试用户注册
 */
async function testRegister() {
  try {
    console.log('🧪 测试用户注册...');
    
    const response = await axios.post(`${API_BASE_URL}/auth/register`, testUser);
    
    console.log('✅ 注册成功:', response.data.message);
    console.log('📝 用户信息:', response.data.data.user.username);
    console.log('🔑 访问令牌:', response.data.data.tokens.accessToken.substring(0, 50) + '...');
    
    return response.data.data.tokens.accessToken;
    
  } catch (error) {
    console.error('❌ 注册失败:', error.response?.data?.error?.message || error.message);
    return null;
  }
}

/**
 * 测试用户登录
 */
async function testLogin() {
  try {
    console.log('\n🧪 测试用户登录...');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    
    console.log('✅ 登录成功:', response.data.message);
    console.log('📝 用户信息:', response.data.data.user.username);
    console.log('🔑 访问令牌:', response.data.data.tokens.accessToken.substring(0, 50) + '...');
    
    return response.data.data.tokens.accessToken;
    
  } catch (error) {
    console.error('❌ 登录失败:', error.response?.data?.error?.message || error.message);
    return null;
  }
}

/**
 * 测试获取当前用户信息
 */
async function testGetCurrentUser(token) {
  try {
    console.log('\n🧪 测试获取当前用户信息...');
    
    const response = await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('✅ 获取用户信息成功:', response.data.message);
    console.log('📝 用户详情:', {
      username: response.data.data.username,
      email: response.data.data.email,
      role: response.data.data.role,
      fullName: response.data.data.fullName
    });
    
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error.response?.data?.error?.message || error.message);
  }
}

/**
 * 测试无效令牌访问
 */
async function testInvalidToken() {
  try {
    console.log('\n🧪 测试无效令牌访问...');
    
    await axios.get(`${API_BASE_URL}/auth/me`, {
      headers: {
        Authorization: 'Bearer invalid-token'
      }
    });
    
  } catch (error) {
    console.log('✅ 无效令牌正确被拒绝:', error.response?.data?.error?.message);
  }
}

/**
 * 运行所有测试
 */
async function runTests() {
  console.log('🚀 开始认证功能测试\n');
  
  // 测试注册
  let token = await testRegister();
  
  // 如果注册失败（可能用户已存在），尝试登录
  if (!token) {
    token = await testLogin();
  }
  
  // 如果有有效令牌，测试受保护的路由
  if (token) {
    await testGetCurrentUser(token);
  }
  
  // 测试无效令牌
  await testInvalidToken();
  
  console.log('\n🎉 认证功能测试完成');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testRegister,
  testLogin,
  testGetCurrentUser,
  testInvalidToken,
  runTests
};
