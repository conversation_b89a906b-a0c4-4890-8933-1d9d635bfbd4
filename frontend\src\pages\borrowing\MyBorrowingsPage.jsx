/**
 * 我的借阅页面
 * 
 * 显示当前用户的借阅记录
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Tag,
  Typography,
  Space,
  Tabs,
  Tooltip,
  Modal,
  Descriptions,
  Image,
  Alert,
} from 'antd';
import {
  BookOutlined,
  ReloadOutlined,
  EyeOutlined,
  UndoOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { borrowingService } from '../../services/borrowingService';
import { BORROWING_STATUS_LABELS } from '../../utils/constants';
import dayjs from 'dayjs';
import toast from 'react-hot-toast';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

function MyBorrowingsPage() {
  const [activeTab, setActiveTab] = useState('current');
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // 获取当前借阅
  const {
    data: currentBorrowings,
    isLoading: currentLoading,
    refetch: refetchCurrent,
  } = useQuery(
    ['currentBorrowings'],
    () => borrowingService.getCurrentBorrowings(),
    {
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );

  // 获取借阅历史
  const {
    data: borrowingHistory,
    isLoading: historyLoading,
    refetch: refetchHistory,
  } = useQuery(
    ['borrowingHistory'],
    () => borrowingService.getBorrowingHistory(),
    {
      enabled: activeTab === 'history',
      staleTime: 5 * 60 * 1000, // 5分钟
    }
  );

  // 归还图书
  const returnBookMutation = useMutation(
    (recordId) => borrowingService.returnBook(recordId),
    {
      onSuccess: () => {
        toast.success('图书归还成功！');
        queryClient.invalidateQueries(['currentBorrowings']);
        queryClient.invalidateQueries(['borrowingHistory']);
      },
      onError: (error) => {
        toast.error(error.response?.data?.error?.message || '归还失败');
      },
    }
  );

  // 续借图书
  const renewBookMutation = useMutation(
    (recordId) => borrowingService.renewBook(recordId),
    {
      onSuccess: () => {
        toast.success('图书续借成功！');
        queryClient.invalidateQueries(['currentBorrowings']);
      },
      onError: (error) => {
        toast.error(error.response?.data?.error?.message || '续借失败');
      },
    }
  );

  // 处理归还图书
  const handleReturnBook = (record) => {
    confirm({
      title: '确认归还图书',
      icon: <ExclamationCircleOutlined />,
      content: `确定要归还《${record.book.title}》吗？`,
      onOk() {
        returnBookMutation.mutate(record._id);
      },
    });
  };

  // 处理续借图书
  const handleRenewBook = (record) => {
    confirm({
      title: '确认续借图书',
      icon: <ClockCircleOutlined />,
      content: `确定要续借《${record.book.title}》吗？`,
      onOk() {
        renewBookMutation.mutate(record._id);
      },
    });
  };

  // 查看图书详情
  const handleViewBook = (bookId) => {
    navigate(`/books/${bookId}`);
  };

  // 查看借阅详情
  const handleViewDetail = (record) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  // 计算逾期天数
  const getOverdueDays = (dueDate, status) => {
    if (status === 'returned') return 0;
    const now = dayjs();
    const due = dayjs(dueDate);
    return now.isAfter(due) ? now.diff(due, 'day') : 0;
  };

  // 当前借阅表格列
  const currentColumns = [
    {
      title: '图书信息',
      dataIndex: 'book',
      key: 'book',
      render: (book) => (
        <div className="flex items-center space-x-3">
          <div className="w-12 h-16 bg-gray-100 rounded flex items-center justify-center">
            {book.coverImage ? (
              <Image
                src={book.coverImage}
                alt={book.title}
                width={48}
                height={64}
                className="object-cover rounded"
              />
            ) : (
              <BookOutlined className="text-gray-400" />
            )}
          </div>
          <div>
            <div className="font-medium">{book.title}</div>
            <div className="text-gray-500 text-sm">{book.author}</div>
            <div className="text-gray-400 text-xs">{book.category}</div>
          </div>
        </div>
      ),
    },
    {
      title: '借阅日期',
      dataIndex: 'borrowDate',
      key: 'borrowDate',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date, record) => {
        const overdueDays = getOverdueDays(date, record.status);
        return (
          <div>
            <div>{dayjs(date).format('YYYY-MM-DD')}</div>
            {overdueDays > 0 && (
              <Tag color="error" size="small">
                逾期 {overdueDays} 天
              </Tag>
            )}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        const overdueDays = getOverdueDays(record.dueDate, status);
        const actualStatus = overdueDays > 0 ? 'overdue' : status;
        const label = BORROWING_STATUS_LABELS[actualStatus];
        return <Tag color={label?.color}>{label?.text}</Tag>;
      },
    },
    {
      title: '续借次数',
      dataIndex: 'renewalCount',
      key: 'renewalCount',
      render: (count) => `${count}/2`,
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="查看图书">
            <Button
              type="text"
              icon={<BookOutlined />}
              onClick={() => handleViewBook(record.book._id)}
            />
          </Tooltip>
          {record.renewalCount < 2 && (
            <Tooltip title="续借">
              <Button
                type="text"
                icon={<ClockCircleOutlined />}
                onClick={() => handleRenewBook(record)}
                loading={renewBookMutation.isLoading}
              />
            </Tooltip>
          )}
          <Tooltip title="归还">
            <Button
              type="primary"
              size="small"
              icon={<UndoOutlined />}
              onClick={() => handleReturnBook(record)}
              loading={returnBookMutation.isLoading}
            >
              归还
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 历史记录表格列
  const historyColumns = [
    {
      title: '图书信息',
      dataIndex: 'book',
      key: 'book',
      render: (book) => (
        <div className="flex items-center space-x-3">
          <div className="w-12 h-16 bg-gray-100 rounded flex items-center justify-center">
            {book.coverImage ? (
              <Image
                src={book.coverImage}
                alt={book.title}
                width={48}
                height={64}
                className="object-cover rounded"
              />
            ) : (
              <BookOutlined className="text-gray-400" />
            )}
          </div>
          <div>
            <div className="font-medium">{book.title}</div>
            <div className="text-gray-500 text-sm">{book.author}</div>
          </div>
        </div>
      ),
    },
    {
      title: '借阅日期',
      dataIndex: 'borrowDate',
      key: 'borrowDate',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '归还日期',
      dataIndex: 'returnDate',
      key: 'returnDate',
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: '借阅天数',
      key: 'borrowingDays',
      render: (_, record) => {
        const start = dayjs(record.borrowDate);
        const end = record.returnDate ? dayjs(record.returnDate) : dayjs();
        return `${end.diff(start, 'day')} 天`;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const label = BORROWING_STATUS_LABELS[status];
        return <Tag color={label?.color}>{label?.text}</Tag>;
      },
    },
    {
      title: '罚金',
      dataIndex: 'fine',
      key: 'fine',
      render: (fine) => fine > 0 ? `¥${fine.toFixed(2)}` : '-',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="查看图书">
            <Button
              type="text"
              icon={<BookOutlined />}
              onClick={() => handleViewBook(record.book._id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2}>我的借阅</Title>
        <Text className="text-gray-600">
          管理您的图书借阅记录
        </Text>
      </div>

      {/* 统计信息 */}
      {currentBorrowings?.data && (
        <div className="mb-6">
          <Alert
            message={
              <div>
                当前借阅：<strong>{currentBorrowings.data.length}</strong> 本图书
                {currentBorrowings.data.some(record =>
                  getOverdueDays(record.dueDate, record.status) > 0
                ) && (
                  <span className="ml-4 text-red-500">
                    ⚠️ 有图书已逾期，请及时归还
                  </span>
                )}
              </div>
            }
            type="info"
            showIcon
          />
        </div>
      )}

      {/* 标签页 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                if (activeTab === 'current') {
                  refetchCurrent();
                } else {
                  refetchHistory();
                }
              }}
            >
              刷新
            </Button>
          }
        >
          <TabPane tab="当前借阅" key="current">
            <Table
              columns={currentColumns}
              dataSource={currentBorrowings?.data || []}
              loading={currentLoading}
              rowKey="_id"
              pagination={false}
              locale={{ emptyText: '暂无借阅记录' }}
            />
          </TabPane>
          
          <TabPane tab="借阅历史" key="history">
            <Table
              columns={historyColumns}
              dataSource={borrowingHistory?.data?.items || []}
              loading={historyLoading}
              rowKey="_id"
              pagination={{
                total: borrowingHistory?.data?.pagination?.totalItems || 0,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              locale={{ emptyText: '暂无历史记录' }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="借阅详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedRecord && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="图书标题">
              {selectedRecord.book.title}
            </Descriptions.Item>
            <Descriptions.Item label="作者">
              {selectedRecord.book.author}
            </Descriptions.Item>
            <Descriptions.Item label="ISBN">
              {selectedRecord.book.isbn || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="借阅日期">
              {dayjs(selectedRecord.borrowDate).format('YYYY-MM-DD HH:mm')}
            </Descriptions.Item>
            <Descriptions.Item label="应还日期">
              {dayjs(selectedRecord.dueDate).format('YYYY-MM-DD')}
            </Descriptions.Item>
            {selectedRecord.returnDate && (
              <Descriptions.Item label="归还日期">
                {dayjs(selectedRecord.returnDate).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="续借次数">
              {selectedRecord.renewalCount}/2
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={BORROWING_STATUS_LABELS[selectedRecord.status]?.color}>
                {BORROWING_STATUS_LABELS[selectedRecord.status]?.text}
              </Tag>
            </Descriptions.Item>
            {selectedRecord.fine > 0 && (
              <Descriptions.Item label="罚金">
                ¥{selectedRecord.fine.toFixed(2)}
              </Descriptions.Item>
            )}
            {selectedRecord.notes && (
              <Descriptions.Item label="备注">
                {selectedRecord.notes}
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
}

export default MyBorrowingsPage;
