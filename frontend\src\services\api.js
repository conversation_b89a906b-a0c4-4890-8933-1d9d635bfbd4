/**
 * Axios API 配置
 * 
 * 配置Axios实例，包括请求拦截器、响应拦截器等
 */

import axios from 'axios';
import { message } from 'antd';

// 从环境变量获取API基础URL
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

// 创建Axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从localStorage获取JWT令牌
    const token = localStorage.getItem('accessToken');
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 在开发环境中记录请求信息
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params,
      });
    }

    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 在开发环境中记录响应信息
    if (import.meta.env.DEV) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    return response;
  },
  (error) => {
    console.error('❌ Response Error:', error);

    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // 未授权 - 清除本地存储的令牌并重定向到登录页
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          
          // 如果不是在登录页面，则重定向到登录页
          if (window.location.pathname !== '/login') {
            message.error('登录已过期，请重新登录');
            window.location.href = '/login';
          }
          break;
          
        case 403:
          // 禁止访问
          message.error(data?.error?.message || '您没有权限执行此操作');
          break;
          
        case 404:
          // 资源不存在
          message.error(data?.error?.message || '请求的资源不存在');
          break;
          
        case 422:
          // 验证错误
          if (data?.error?.details) {
            // 显示详细的验证错误信息
            const errorMessages = data.error.details.map(detail => detail.message).join(', ');
            message.error(errorMessages);
          } else {
            message.error(data?.error?.message || '请求参数验证失败');
          }
          break;
          
        case 429:
          // 请求过于频繁
          message.error('请求过于频繁，请稍后再试');
          break;
          
        case 500:
          // 服务器内部错误
          message.error('服务器内部错误，请稍后再试');
          break;
          
        default:
          // 其他错误
          message.error(data?.error?.message || `请求失败 (${status})`);
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      message.error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      message.error('请求配置错误');
    }

    return Promise.reject(error);
  }
);

// 导出API实例
export default api;

// 导出常用的HTTP方法
export const get = (url, config) => api.get(url, config);
export const post = (url, data, config) => api.post(url, data, config);
export const put = (url, data, config) => api.put(url, data, config);
export const patch = (url, data, config) => api.patch(url, data, config);
export const del = (url, config) => api.delete(url, config);

// 导出API基础URL
export { API_BASE_URL };

// 令牌管理工具函数
export const tokenManager = {
  // 获取访问令牌
  getAccessToken: () => localStorage.getItem('accessToken'),
  
  // 设置访问令牌
  setAccessToken: (token) => localStorage.setItem('accessToken', token),
  
  // 获取刷新令牌
  getRefreshToken: () => localStorage.getItem('refreshToken'),
  
  // 设置刷新令牌
  setRefreshToken: (token) => localStorage.setItem('refreshToken', token),
  
  // 清除所有令牌
  clearTokens: () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  },
  
  // 检查是否已登录
  isAuthenticated: () => !!localStorage.getItem('accessToken'),
};

// 刷新令牌函数
export const refreshAccessToken = async () => {
  try {
    const refreshToken = tokenManager.getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
      refreshToken,
    });

    const { accessToken, refreshToken: newRefreshToken } = response.data.data.tokens;
    
    tokenManager.setAccessToken(accessToken);
    if (newRefreshToken) {
      tokenManager.setRefreshToken(newRefreshToken);
    }

    return accessToken;
  } catch (error) {
    console.error('Failed to refresh token:', error);
    tokenManager.clearTokens();
    throw error;
  }
};
