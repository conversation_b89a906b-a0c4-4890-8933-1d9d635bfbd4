/**
 * 统计报表页面（管理员）
 * 
 * 显示系统的各种统计数据和图表
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  DatePicker,
  Select,
  Space,
  Table,
  Progress,
  Tag,
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  ReadOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { bookService } from '../../services/bookService';
import { borrowingService } from '../../services/borrowingService';
import { userService } from '../../services/userService';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

function StatisticsPage() {
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [period, setPeriod] = useState('30days');

  // 获取图书统计
  const { data: bookStats } = useQuery(
    ['bookStats'],
    () => bookService.getBookStats(),
    {
      staleTime: 5 * 60 * 1000,
    }
  );

  // 获取借阅统计
  const { data: borrowingStats } = useQuery(
    ['borrowingStats', dateRange],
    () => borrowingService.getBorrowingStats({
      startDate: dateRange[0]?.format('YYYY-MM-DD'),
      endDate: dateRange[1]?.format('YYYY-MM-DD'),
    }),
    {
      staleTime: 5 * 60 * 1000,
    }
  );

  // 获取用户统计
  const { data: userStats } = useQuery(
    ['userStats'],
    () => userService.getUserStats(),
    {
      staleTime: 5 * 60 * 1000,
    }
  );

  // 获取热门图书
  const { data: popularBooks } = useQuery(
    ['popularBooks'],
    () => bookService.getPopularBooks(10),
    {
      staleTime: 5 * 60 * 1000,
    }
  );

  // 获取逾期记录
  const { data: overdueRecords } = useQuery(
    ['overdueRecords'],
    () => borrowingService.getOverdueRecords({ limit: 10 }),
    {
      staleTime: 5 * 60 * 1000,
    }
  );

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 处理预设时间段变化
  const handlePeriodChange = (value) => {
    setPeriod(value);
    const now = dayjs();
    let start;
    
    switch (value) {
      case '7days':
        start = now.subtract(7, 'day');
        break;
      case '30days':
        start = now.subtract(30, 'day');
        break;
      case '90days':
        start = now.subtract(90, 'day');
        break;
      case '1year':
        start = now.subtract(1, 'year');
        break;
      default:
        start = now.subtract(30, 'day');
    }
    
    setDateRange([start, now]);
  };

  // 热门图书表格列
  const popularBooksColumns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => (
        <div className="flex items-center">
          {index < 3 ? (
            <TrophyOutlined 
              className={`text-lg ${
                index === 0 ? 'text-yellow-500' : 
                index === 1 ? 'text-gray-400' : 
                'text-orange-400'
              }`} 
            />
          ) : (
            <span className="text-gray-500">{index + 1}</span>
          )}
        </div>
      ),
    },
    {
      title: '图书信息',
      key: 'bookInfo',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.title}</div>
          <div className="text-gray-500 text-sm">{record.author}</div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: '借阅次数',
      dataIndex: ['borrowingStats', 'totalBorrowed'],
      key: 'totalBorrowed',
      render: (count) => (
        <div className="flex items-center">
          <span className="mr-2">{count || 0}</span>
          <Progress 
            percent={Math.min((count || 0) / 50 * 100, 100)} 
            size="small" 
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '当前状态',
      key: 'status',
      render: (_, record) => (
        <Tag color={record.stock > 0 ? 'green' : 'red'}>
          {record.stock > 0 ? '可借阅' : '已借完'}
        </Tag>
      ),
    },
  ];

  // 逾期记录表格列
  const overdueColumns = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.user?.username}</div>
          <div className="text-gray-500 text-sm">{record.user?.email}</div>
        </div>
      ),
    },
    {
      title: '图书',
      key: 'book',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.book?.title}</div>
          <div className="text-gray-500 text-sm">{record.book?.author}</div>
        </div>
      ),
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date) => (
        <div className="text-red-500">
          {new Date(date).toLocaleDateString()}
        </div>
      ),
    },
    {
      title: '逾期天数',
      key: 'overdueDays',
      render: (_, record) => {
        const days = Math.ceil((new Date() - new Date(record.dueDate)) / (1000 * 60 * 60 * 24));
        return (
          <Tag color="red">
            {days} 天
          </Tag>
        );
      },
    },
    {
      title: '罚金',
      dataIndex: 'fine',
      key: 'fine',
      render: (fine) => (
        <span className="text-red-500">
          ¥{fine || 0}
        </span>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6 flex items-center justify-between">
        <Title level={2}>
          <RiseOutlined className="mr-2" />
          统计报表
        </Title>
        
        <Space>
          <Select
            value={period}
            onChange={handlePeriodChange}
            style={{ width: 120 }}
          >
            <Option value="7days">最近7天</Option>
            <Option value="30days">最近30天</Option>
            <Option value="90days">最近90天</Option>
            <Option value="1year">最近1年</Option>
          </Select>
          
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            format="YYYY-MM-DD"
          />
        </Space>
      </div>

      {/* 总体统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="图书总数"
              value={bookStats?.data?.totalBooks || 0}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={userStats?.data?.totalUsers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总借阅次数"
              value={borrowingStats?.data?.totalBorrowings || 0}
              prefix={<ReadOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="逾期记录"
              value={overdueRecords?.data?.items?.length || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={16} className="mb-6">
        <Col span={8}>
          <Card title="借阅状态分布">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>活跃借阅</span>
                <span className="font-medium text-blue-600">
                  {borrowingStats?.data?.activeBorrowings || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>已归还</span>
                <span className="font-medium text-green-600">
                  {borrowingStats?.data?.returnedBorrowings || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>逾期未还</span>
                <span className="font-medium text-red-600">
                  {borrowingStats?.data?.overdueBorrowings || 0}
                </span>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="图书状态分布">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>可借阅</span>
                <span className="font-medium text-green-600">
                  {bookStats?.data?.availableBooks || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>已借出</span>
                <span className="font-medium text-blue-600">
                  {bookStats?.data?.borrowedBooks || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>维护中</span>
                <span className="font-medium text-orange-600">
                  {bookStats?.data?.maintenanceBooks || 0}
                </span>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="财务统计">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>总罚金</span>
                <span className="font-medium text-red-600">
                  ¥{borrowingStats?.data?.totalFines || 0}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>平均借阅天数</span>
                <span className="font-medium text-blue-600">
                  {Math.round(borrowingStats?.data?.averageBorrowingDays || 0)} 天
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span>活跃用户</span>
                <span className="font-medium text-green-600">
                  {userStats?.data?.activeUsers || 0}
                </span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 热门图书排行 */}
      <Row gutter={16} className="mb-6">
        <Col span={12}>
          <Card title="热门图书排行" extra={<TrophyOutlined />}>
            <Table
              columns={popularBooksColumns}
              dataSource={popularBooks?.data?.slice(0, 10) || []}
              pagination={false}
              size="small"
              rowKey="_id"
            />
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="逾期记录" extra={<ClockCircleOutlined />}>
            <Table
              columns={overdueColumns}
              dataSource={overdueRecords?.data?.items?.slice(0, 10) || []}
              pagination={false}
              size="small"
              rowKey="_id"
            />
          </Card>
        </Col>
      </Row>

      {/* 分类统计 */}
      <Card title="图书分类统计">
        <Row gutter={16}>
          {bookStats?.data?.categoryStats?.map((category, index) => (
            <Col span={6} key={index} className="mb-4">
              <Card size="small">
                <Statistic
                  title={category._id}
                  value={category.count}
                  prefix={<BookOutlined />}
                  suffix="本"
                />
                <Progress 
                  percent={Math.round((category.count / (bookStats?.data?.totalBooks || 1)) * 100)}
                  size="small"
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  );
}

export default StatisticsPage;
