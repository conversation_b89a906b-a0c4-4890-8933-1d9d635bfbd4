/**
 * 借阅路由 (Borrowing Routes)
 * 
 * 定义图书借阅相关的API端点
 */

const express = require('express');
const { query, param } = require('express-validator');
const {
  borrowBook,
  returnBook,
  renewBook,
  getMyBorrowingRecords,
  getCurrentBorrowings,
  getAllBorrowingRecords,
  getOverdueRecords
} = require('../controllers/borrowing.controller');
const { protect } = require('../middlewares/auth.middleware');
const { adminOnly, memberAndAbove } = require('../middlewares/role.middleware');
const { validate } = require('../middlewares/validation.middleware');

const router = express.Router();

// 参数验证规则
const bookIdValidation = [
  param('bookId')
    .isMongoId()
    .withMessage('无效的图书ID')
];

const recordIdValidation = [
  param('recordId')
    .isMongoId()
    .withMessage('无效的借阅记录ID')
];

const borrowingQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  
  query('status')
    .optional()
    .isIn(['active', 'returned', 'overdue', 'lost', 'all'])
    .withMessage('状态参数无效'),
  
  query('sortBy')
    .optional()
    .isIn(['borrowDate', 'dueDate', 'returnDate', 'status'])
    .withMessage('排序字段无效'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序顺序必须是asc或desc'),
  
  query('userId')
    .optional()
    .isMongoId()
    .withMessage('无效的用户ID'),
  
  query('bookId')
    .optional()
    .isMongoId()
    .withMessage('无效的图书ID')
];

// 会员及以上权限路由
router.post('/borrow/:bookId', protect, memberAndAbove, bookIdValidation, validate, borrowBook);
router.patch('/:recordId/return', protect, memberAndAbove, recordIdValidation, validate, returnBook);
router.patch('/:recordId/renew', protect, memberAndAbove, recordIdValidation, validate, renewBook);
router.get('/my-records', protect, memberAndAbove, borrowingQueryValidation, validate, getMyBorrowingRecords);
router.get('/current', protect, memberAndAbove, getCurrentBorrowings);

// 管理员专用路由
router.get('/', protect, adminOnly, borrowingQueryValidation, validate, getAllBorrowingRecords);
router.get('/overdue', protect, adminOnly, getOverdueRecords);

module.exports = router;
