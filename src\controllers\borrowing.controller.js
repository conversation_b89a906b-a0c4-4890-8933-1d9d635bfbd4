/**
 * 借阅管理控制器 (修复版 - 移除事务)
 * 
 * 处理图书借阅、归还、续借等操作
 */

const mongoose = require('mongoose');
const BorrowingRecord = require('../models/BorrowingRecord');
const Book = require('../models/Book');
const User = require('../models/User');
const { AppError, asyncHandler } = require('../middlewares/error.middleware');
const { successResponse } = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 借阅图书
 * @route POST /api/borrowings/borrow/:bookId
 * @access Private (Member and above)
 */
const borrowBook = asyncHandler(async (req, res) => {
  const { bookId } = req.params;
  const userId = req.user.userId;

  try {
    // 查找图书
    const book = await Book.findById(bookId);
    
    if (!book) {
      throw new AppError('图书不存在', 404, 'BOOK_NOT_FOUND');
    }

    // 检查图书状态
    if (book.status === 'maintenance') {
      throw new AppError('图书正在维护中，暂时无法借阅', 400, 'BOOK_UNDER_MAINTENANCE');
    }

    if (book.status === 'retired') {
      throw new AppError('图书已下架，无法借阅', 400, 'BOOK_RETIRED');
    }

    // 检查库存
    if (book.stock <= 0) {
      throw new AppError('图书库存不足，无法借阅', 400, 'INSUFFICIENT_STOCK');
    }

    // 查找用户
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 检查用户是否可以借阅更多图书
    if (!user.canBorrowMore()) {
      const maxBooks = process.env.MAX_BOOKS_PER_USER || 5;
      throw new AppError(`您已达到最大借阅数量限制 (${maxBooks}本)`, 400, 'MAX_BORROWING_LIMIT_REACHED');
    }

    // 检查用户是否已经借阅了这本书
    const existingBorrowing = await BorrowingRecord.findOne({
      user: userId,
      book: bookId,
      status: 'active'
    });

    if (existingBorrowing) {
      throw new AppError('您已经借阅了这本图书', 400, 'BOOK_ALREADY_BORROWED');
    }

    // 计算应还日期
    const borrowingPeriod = parseInt(process.env.DEFAULT_BORROWING_PERIOD) || 30;
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + borrowingPeriod);

    // 创建借阅记录
    const borrowingRecord = await BorrowingRecord.create({
      book: bookId,
      user: userId,
      borrowDate: new Date(),
      dueDate,
      status: 'active'
    });

    // 更新图书库存
    await book.borrowBook();

    // 更新用户借阅统计
    user.borrowingStats.currentBorrowed += 1;
    user.borrowingStats.totalBorrowed += 1;
    await user.save();

    // 记录日志
    logger.info(`图书借阅: ${user.username} 借阅了 ${book.title}`);

    // 查询完整的借阅记录信息
    const fullBorrowingRecord = await BorrowingRecord.findById(borrowingRecord._id)
      .populate('book', 'title author isbn coverImage')
      .populate('user', 'username email profile');

    res.status(201).json(successResponse(fullBorrowingRecord, '图书借阅成功'));

  } catch (error) {
    throw error;
  }
});

/**
 * 归还图书
 * @route PATCH /api/borrowings/:recordId/return
 * @access Private (Member and above)
 */
const returnBook = asyncHandler(async (req, res) => {
  const { recordId } = req.params;
  const userId = req.user.userId;

  try {
    // 查找借阅记录
    const borrowingRecord = await BorrowingRecord.findById(recordId)
      .populate('book')
      .populate('user');

    if (!borrowingRecord) {
      throw new AppError('借阅记录不存在', 404, 'BORROWING_RECORD_NOT_FOUND');
    }

    // 检查权限（用户只能归还自己的借阅记录，管理员可以归还任何记录）
    if (req.user.role !== 'admin' && borrowingRecord.user._id.toString() !== userId) {
      throw new AppError('您只能归还自己的借阅记录', 403, 'UNAUTHORIZED_RETURN');
    }

    if (borrowingRecord.status !== 'active') {
      throw new AppError('该借阅记录已处理，无法重复归还', 400, 'RECORD_ALREADY_PROCESSED');
    }

    if (borrowingRecord.status === 'lost') {
      throw new AppError('图书已标记为丢失，请联系管理员', 400, 'BOOK_MARKED_AS_LOST');
    }

    // 归还图书
    await borrowingRecord.returnBook();

    // 更新图书库存
    const book = borrowingRecord.book;
    await book.returnBook();

    // 更新用户借阅统计
    const user = borrowingRecord.user;
    user.borrowingStats.currentBorrowed = Math.max(0, user.borrowingStats.currentBorrowed - 1);
    if (borrowingRecord.fine > 0) {
      user.borrowingStats.overdueFines += borrowingRecord.fine;
    }
    await user.save();

    // 记录日志
    logger.info(`图书归还: ${user.username} 归还了 ${book.title}${borrowingRecord.fine > 0 ? ` (罚金: ¥${borrowingRecord.fine})` : ''}`);

    // 获取更新后的借阅记录
    const updatedRecord = await BorrowingRecord.findById(recordId)
      .populate('book', 'title author isbn coverImage')
      .populate('user', 'username email profile');

    res.json(successResponse(updatedRecord, '图书归还成功'));

  } catch (error) {
    throw error;
  }
});

/**
 * 续借图书
 * @route PATCH /api/borrowings/:recordId/renew
 * @access Private (Member and above)
 */
const renewBook = asyncHandler(async (req, res) => {
  const { recordId } = req.params;
  const userId = req.user.userId;

  try {
    // 查找借阅记录
    const borrowingRecord = await BorrowingRecord.findById(recordId)
      .populate('book')
      .populate('user');

    if (!borrowingRecord) {
      throw new AppError('借阅记录不存在', 404, 'BORROWING_RECORD_NOT_FOUND');
    }

    // 检查权限
    if (req.user.role !== 'admin' && borrowingRecord.user._id.toString() !== userId) {
      throw new AppError('您只能续借自己的借阅记录', 403, 'UNAUTHORIZED_RENEWAL');
    }

    if (borrowingRecord.status !== 'active') {
      throw new AppError('只有活跃的借阅记录才能续借', 400, 'INVALID_RECORD_STATUS');
    }

    // 检查续借次数限制
    const maxRenewals = parseInt(process.env.MAX_RENEWALS) || 2;
    if (borrowingRecord.renewalCount >= maxRenewals) {
      throw new AppError(`已达到最大续借次数限制 (${maxRenewals}次)`, 400, 'MAX_RENEWALS_EXCEEDED');
    }

    // 检查是否逾期
    if (borrowingRecord.isOverdue) {
      throw new AppError('逾期图书无法续借，请先归还并缴纳罚金', 400, 'OVERDUE_BOOK_CANNOT_RENEW');
    }

    // 续借图书
    await borrowingRecord.renewBook();

    // 记录日志
    logger.info(`图书续借: ${borrowingRecord.user.username} 续借了 ${borrowingRecord.book.title}`);

    // 获取更新后的借阅记录
    const updatedRecord = await BorrowingRecord.findById(recordId)
      .populate('book', 'title author isbn coverImage')
      .populate('user', 'username email profile');

    res.json(successResponse(updatedRecord, '图书续借成功'));

  } catch (error) {
    throw error;
  }
});

/**
 * 获取个人借阅记录
 * @route GET /api/borrowings/my-records
 * @access Private (Member and above)
 */
const getMyBorrowingRecords = asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const {
    page = 1,
    limit = 10,
    status = 'all',
    sortBy = 'borrowDate',
    sortOrder = 'desc'
  } = req.query;

  // 构建查询条件
  const query = { user: userId };
  
  if (status !== 'all') {
    query.status = status;
  }

  // 构建排序条件
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // 分页查询
  const skip = (page - 1) * limit;
  
  const [records, total] = await Promise.all([
    BorrowingRecord.find(query)
      .populate('book', 'title author isbn coverImage category')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit)),
    BorrowingRecord.countDocuments(query)
  ]);

  // 分页信息
  const pagination = {
    currentPage: parseInt(page),
    totalPages: Math.ceil(total / limit),
    totalItems: total,
    itemsPerPage: parseInt(limit),
    hasNextPage: page < Math.ceil(total / limit),
    hasPrevPage: page > 1
  };

  res.json(successResponse({
    items: records,
    pagination
  }, '获取借阅记录成功'));
});

/**
 * 获取当前借阅的图书
 * @route GET /api/borrowings/current
 * @access Private (Member and above)
 */
const getCurrentBorrowings = asyncHandler(async (req, res) => {
  const userId = req.user.userId;

  const currentBorrowings = await BorrowingRecord.find({
    user: userId,
    status: 'active'
  })
    .populate('book', 'title author isbn coverImage category')
    .sort({ borrowDate: -1 });

  res.json(successResponse(currentBorrowings, '获取当前借阅成功'));
});

/**
 * 获取所有借阅记录（管理员）
 * @route GET /api/borrowings
 * @access Private (Admin only)
 */
const getAllBorrowingRecords = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    status = 'all',
    sortBy = 'borrowDate',
    sortOrder = 'desc',
    search = ''
  } = req.query;

  // 构建查询条件
  const query = {};

  if (status !== 'all') {
    query.status = status;
  }

  // 构建排序条件
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // 分页查询
  const skip = (page - 1) * limit;

  let aggregationPipeline = [
    {
      $lookup: {
        from: 'books',
        localField: 'book',
        foreignField: '_id',
        as: 'book'
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$book'
    },
    {
      $unwind: '$user'
    }
  ];

  // 添加搜索条件
  if (search) {
    aggregationPipeline.push({
      $match: {
        $or: [
          { 'book.title': { $regex: search, $options: 'i' } },
          { 'book.author': { $regex: search, $options: 'i' } },
          { 'user.username': { $regex: search, $options: 'i' } },
          { 'user.email': { $regex: search, $options: 'i' } }
        ]
      }
    });
  }

  // 添加状态筛选
  if (status !== 'all') {
    aggregationPipeline.push({
      $match: { status }
    });
  }

  // 添加排序
  aggregationPipeline.push({ $sort: sort });

  // 分页
  const [records, totalResult] = await Promise.all([
    BorrowingRecord.aggregate([
      ...aggregationPipeline,
      { $skip: skip },
      { $limit: parseInt(limit) },
      {
        $project: {
          _id: 1,
          borrowDate: 1,
          dueDate: 1,
          returnDate: 1,
          status: 1,
          fine: 1,
          renewalCount: 1,
          'book._id': 1,
          'book.title': 1,
          'book.author': 1,
          'book.isbn': 1,
          'book.coverImage': 1,
          'book.category': 1,
          'user._id': 1,
          'user.username': 1,
          'user.email': 1,
          'user.profile': 1
        }
      }
    ]),
    BorrowingRecord.aggregate([
      ...aggregationPipeline,
      { $count: 'total' }
    ])
  ]);

  const total = totalResult[0]?.total || 0;

  // 分页信息
  const pagination = {
    currentPage: parseInt(page),
    totalPages: Math.ceil(total / limit),
    totalItems: total,
    itemsPerPage: parseInt(limit),
    hasNextPage: page < Math.ceil(total / limit),
    hasPrevPage: page > 1
  };

  res.json(successResponse({
    items: records,
    pagination
  }, '获取借阅记录成功'));
});

/**
 * 获取逾期记录（管理员）
 * @route GET /api/borrowings/overdue
 * @access Private (Admin only)
 */
const getOverdueRecords = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    sortBy = 'dueDate',
    sortOrder = 'asc'
  } = req.query;

  // 构建查询条件 - 查找逾期的活跃借阅记录
  const query = {
    status: 'active',
    dueDate: { $lt: new Date() }
  };

  // 构建排序条件
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // 分页查询
  const skip = (page - 1) * limit;

  const [records, total] = await Promise.all([
    BorrowingRecord.find(query)
      .populate('book', 'title author isbn coverImage category')
      .populate('user', 'username email profile')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit)),
    BorrowingRecord.countDocuments(query)
  ]);

  // 分页信息
  const pagination = {
    currentPage: parseInt(page),
    totalPages: Math.ceil(total / limit),
    totalItems: total,
    itemsPerPage: parseInt(limit),
    hasNextPage: page < Math.ceil(total / limit),
    hasPrevPage: page > 1
  };

  res.json(successResponse({
    items: records,
    pagination
  }, '获取逾期记录成功'));
});

module.exports = {
  borrowBook,
  returnBook,
  renewBook,
  getMyBorrowingRecords,
  getCurrentBorrowings,
  getAllBorrowingRecords,
  getOverdueRecords
};
