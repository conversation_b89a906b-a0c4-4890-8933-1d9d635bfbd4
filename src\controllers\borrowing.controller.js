/**
 * 借阅控制器 (Borrowing Controller)
 * 
 * 处理图书借阅和归还相关功能
 */

const mongoose = require('mongoose');
const { Book, BorrowingRecord, User, Notification } = require('../models');
const { successResponse, paginatedResponse, errorResponse } = require('../utils/response');
const { asyncHandler, AppError } = require('../middlewares/error.middleware');
const logger = require('../utils/logger');

/**
 * 借阅图书
 * @route POST /api/borrowings/borrow/:bookId
 * @access Private (Member and above)
 */
const borrowBook = asyncHandler(async (req, res) => {
  const { bookId } = req.params;
  const userId = req.user.userId;

  // 使用事务确保操作的原子性
  const session = await mongoose.startSession();
  
  try {
    await session.withTransaction(async () => {
      // 查找图书
      const book = await Book.findById(bookId).session(session);
      
      if (!book) {
        throw new AppError('图书不存在', 404, 'BOOK_NOT_FOUND');
      }

      // 检查图书状态
      if (book.status === 'maintenance') {
        throw new AppError('图书正在维护中，暂时无法借阅', 400, 'BOOK_UNDER_MAINTENANCE');
      }

      if (book.status === 'retired') {
        throw new AppError('图书已下架，无法借阅', 400, 'BOOK_RETIRED');
      }

      // 检查库存
      if (book.stock <= 0) {
        throw new AppError('图书库存不足，无法借阅', 400, 'INSUFFICIENT_STOCK');
      }

      // 查找用户
      const user = await User.findById(userId).session(session);
      
      if (!user) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 检查用户是否可以借阅更多图书
      if (!user.canBorrowMore()) {
        const maxBooks = process.env.MAX_BOOKS_PER_USER || 5;
        throw new AppError(`您已达到最大借阅数量限制 (${maxBooks}本)`, 400, 'MAX_BORROWING_LIMIT_REACHED');
      }

      // 检查用户是否已经借阅了这本书
      const existingBorrowing = await BorrowingRecord.findOne({
        user: userId,
        book: bookId,
        status: 'active'
      }).session(session);

      if (existingBorrowing) {
        throw new AppError('您已经借阅了这本图书', 400, 'BOOK_ALREADY_BORROWED');
      }

      // 计算应还日期
      const borrowingPeriod = parseInt(process.env.DEFAULT_BORROWING_PERIOD) || 30;
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + borrowingPeriod);

      // 创建借阅记录
      const borrowingRecord = await BorrowingRecord.create([{
        book: bookId,
        user: userId,
        borrowDate: new Date(),
        dueDate,
        status: 'active'
      }], { session });

      // 更新图书库存
      await book.borrowBook();
      await book.save({ session });

      // 更新用户借阅统计
      user.borrowingStats.currentBorrowed += 1;
      user.borrowingStats.totalBorrowed += 1;
      await user.save({ session });

      // 创建通知
      await Notification.create([{
        user: userId,
        type: 'return_success',
        title: '借阅成功',
        message: `您已成功借阅《${book.title}》，请于 ${dueDate.toLocaleDateString()} 前归还。`,
        relatedRecord: borrowingRecord[0]._id
      }], { session });

      // 记录日志
      logger.info(`图书借阅: ${user.username} 借阅了 ${book.title}`);

      // 返回借阅记录（需要在事务外查询以获取populate数据）
      req.borrowingRecordId = borrowingRecord[0]._id;
    });

    // 事务成功后，查询完整的借阅记录信息
    const borrowingRecord = await BorrowingRecord.findById(req.borrowingRecordId)
      .populate('book', 'title author isbn coverImage')
      .populate('user', 'username email profile');

    res.status(201).json(successResponse(borrowingRecord, '图书借阅成功'));

  } catch (error) {
    throw error;
  } finally {
    await session.endSession();
  }
});

/**
 * 归还图书
 * @route PATCH /api/borrowings/:recordId/return
 * @access Private (Member and above)
 */
const returnBook = asyncHandler(async (req, res) => {
  const { recordId } = req.params;
  const userId = req.user.userId;

  // 使用事务确保操作的原子性
  const session = await mongoose.startSession();
  
  try {
    await session.withTransaction(async () => {
      // 查找借阅记录
      const borrowingRecord = await BorrowingRecord.findById(recordId)
        .populate('book')
        .populate('user')
        .session(session);

      if (!borrowingRecord) {
        throw new AppError('借阅记录不存在', 404, 'BORROWING_RECORD_NOT_FOUND');
      }

      // 检查权限（只能归还自己的借阅或管理员可以操作任何借阅）
      if (borrowingRecord.user._id.toString() !== userId && req.user.role !== 'admin') {
        throw new AppError('您只能归还自己的借阅', 403, 'UNAUTHORIZED_RETURN');
      }

      // 检查借阅状态
      if (borrowingRecord.status === 'returned') {
        throw new AppError('图书已经归还', 400, 'BOOK_ALREADY_RETURNED');
      }

      if (borrowingRecord.status === 'lost') {
        throw new AppError('图书已标记为丢失，请联系管理员', 400, 'BOOK_MARKED_AS_LOST');
      }

      // 归还图书
      await borrowingRecord.returnBook();
      await borrowingRecord.save({ session });

      // 更新图书库存
      const book = borrowingRecord.book;
      await book.returnBook();
      await book.save({ session });

      // 更新用户借阅统计
      const user = borrowingRecord.user;
      user.borrowingStats.currentBorrowed = Math.max(0, user.borrowingStats.currentBorrowed - 1);
      if (borrowingRecord.fine > 0) {
        user.borrowingStats.overdueFines += borrowingRecord.fine;
      }
      await user.save({ session });

      // 创建归还成功通知
      const notificationMessage = borrowingRecord.fine > 0 
        ? `您已成功归还《${book.title}》，产生逾期罚金 ¥${borrowingRecord.fine.toFixed(2)}。`
        : `您已成功归还《${book.title}》。`;

      await Notification.create([{
        user: borrowingRecord.user._id,
        type: 'return_success',
        title: '归还成功',
        message: notificationMessage,
        relatedRecord: borrowingRecord._id
      }], { session });

      // 记录日志
      logger.info(`图书归还: ${user.username} 归还了 ${book.title}${borrowingRecord.fine > 0 ? ` (罚金: ¥${borrowingRecord.fine})` : ''}`);
    });

    // 查询更新后的借阅记录
    const updatedRecord = await BorrowingRecord.findById(recordId)
      .populate('book', 'title author isbn coverImage')
      .populate('user', 'username email profile');

    res.json(successResponse(updatedRecord, '图书归还成功'));

  } catch (error) {
    throw error;
  } finally {
    await session.endSession();
  }
});

/**
 * 续借图书
 * @route PATCH /api/borrowings/:recordId/renew
 * @access Private (Member and above)
 */
const renewBook = asyncHandler(async (req, res) => {
  const { recordId } = req.params;
  const userId = req.user.userId;

  // 查找借阅记录
  const borrowingRecord = await BorrowingRecord.findById(recordId)
    .populate('book', 'title author')
    .populate('user', 'username email');

  if (!borrowingRecord) {
    throw new AppError('借阅记录不存在', 404, 'BORROWING_RECORD_NOT_FOUND');
  }

  // 检查权限
  if (borrowingRecord.user._id.toString() !== userId && req.user.role !== 'admin') {
    throw new AppError('您只能续借自己的借阅', 403, 'UNAUTHORIZED_RENEWAL');
  }

  // 续借图书
  await borrowingRecord.renew();

  // 创建续借通知
  await Notification.create({
    user: borrowingRecord.user._id,
    type: 'due_reminder',
    title: '续借成功',
    message: `您已成功续借《${borrowingRecord.book.title}》，新的归还日期为 ${borrowingRecord.dueDate.toLocaleDateString()}。`,
    relatedRecord: borrowingRecord._id
  });

  // 记录日志
  logger.info(`图书续借: ${borrowingRecord.user.username} 续借了 ${borrowingRecord.book.title}`);

  res.json(successResponse(borrowingRecord, '图书续借成功'));
});

/**
 * 获取当前用户的借阅记录
 * @route GET /api/borrowings/my-records
 * @access Private (Member and above)
 */
const getMyBorrowingRecords = asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'borrowDate',
    sortOrder = 'desc'
  } = req.query;

  const records = await BorrowingRecord.getUserBorrowingHistory(userId, {
    page: parseInt(page),
    limit: parseInt(limit),
    status,
    sortBy,
    sortOrder
  });

  const total = await BorrowingRecord.countDocuments({
    user: userId,
    ...(status && status !== 'all' ? { status } : {})
  });

  res.json(paginatedResponse(records, {
    page: parseInt(page),
    limit: parseInt(limit),
    total
  }, '获取借阅记录成功'));
});

/**
 * 获取当前借阅中的图书
 * @route GET /api/borrowings/current
 * @access Private (Member and above)
 */
const getCurrentBorrowings = asyncHandler(async (req, res) => {
  const userId = req.user.userId;

  const currentBorrowings = await BorrowingRecord.find({
    user: userId,
    status: 'active'
  })
  .populate('book', 'title author isbn coverImage category')
  .sort({ borrowDate: -1 });

  res.json(successResponse(currentBorrowings, '获取当前借阅成功'));
});

/**
 * 获取所有借阅记录（管理员）
 * @route GET /api/borrowings
 * @access Private (Admin only)
 */
const getAllBorrowingRecords = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    userId,
    bookId,
    status,
    sortBy = 'borrowDate',
    sortOrder = 'desc'
  } = req.query;

  // 构建查询条件
  const query = {};
  if (userId) query.user = userId;
  if (bookId) query.book = bookId;
  if (status && status !== 'all') query.status = status;

  // 排序设置
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // 分页设置
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  // 执行查询
  const [records, total] = await Promise.all([
    BorrowingRecord.find(query)
      .populate('book', 'title author isbn category')
      .populate('user', 'username email profile')
      .sort(sort)
      .skip(skip)
      .limit(limitNum),
    BorrowingRecord.countDocuments(query)
  ]);

  res.json(paginatedResponse(records, {
    page: pageNum,
    limit: limitNum,
    total
  }, '获取借阅记录成功'));
});

/**
 * 获取逾期借阅记录
 * @route GET /api/borrowings/overdue
 * @access Private (Admin only)
 */
const getOverdueRecords = asyncHandler(async (req, res) => {
  const overdueRecords = await BorrowingRecord.getOverdueRecords();

  res.json(successResponse(overdueRecords, '获取逾期记录成功'));
});

module.exports = {
  borrowBook,
  returnBook,
  renewBook,
  getMyBorrowingRecords,
  getCurrentBorrowings,
  getAllBorrowingRecords,
  getOverdueRecords
};
