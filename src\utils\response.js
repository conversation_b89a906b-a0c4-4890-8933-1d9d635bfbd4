/**
 * 统一响应格式工具
 * 
 * 提供标准化的API响应格式
 */

/**
 * 成功响应格式
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} statusCode - HTTP状态码
 * @returns {Object} 格式化的成功响应
 */
const successResponse = (data = null, message = '操作成功', statusCode = 200) => {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  };
};

/**
 * 分页响应格式
 * @param {Array} items - 数据项数组
 * @param {Object} pagination - 分页信息
 * @param {string} message - 响应消息
 * @returns {Object} 格式化的分页响应
 */
const paginatedResponse = (items, pagination, message = '获取成功') => {
  return {
    success: true,
    data: {
      items,
      pagination: {
        currentPage: pagination.page,
        totalPages: Math.ceil(pagination.total / pagination.limit),
        totalItems: pagination.total,
        itemsPerPage: pagination.limit,
        hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    },
    message,
    timestamp: new Date().toISOString()
  };
};

/**
 * 错误响应格式
 * @param {string} code - 错误代码
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP状态码
 * @param {Array} details - 错误详情
 * @returns {Object} 格式化的错误响应
 */
const errorResponse = (code, message, statusCode = 500, details = null) => {
  const response = {
    success: false,
    error: {
      code,
      message
    },
    timestamp: new Date().toISOString()
  };

  if (details) {
    response.error.details = details;
  }

  return response;
};

/**
 * 验证错误响应格式
 * @param {Array} errors - 验证错误数组
 * @returns {Object} 格式化的验证错误响应
 */
const validationErrorResponse = (errors) => {
  return errorResponse(
    'VALIDATION_ERROR',
    '请求参数验证失败',
    400,
    errors
  );
};

module.exports = {
  successResponse,
  paginatedResponse,
  errorResponse,
  validationErrorResponse
};
