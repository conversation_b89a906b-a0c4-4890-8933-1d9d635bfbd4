/**
 * JWT配置文件
 * 
 * JWT令牌生成和验证相关配置
 */

const jwt = require('jsonwebtoken');

/**
 * 生成访问令牌
 * @param {Object} payload - JWT载荷数据
 * @returns {string} JWT令牌
 */
const generateAccessToken = (payload) => {
  return jwt.sign(
    payload,
    process.env.JWT_SECRET || 'your-default-secret-key',
    {
      expiresIn: process.env.JWT_EXPIRE || '7d',
      issuer: 'library-management-system',
      audience: 'library-users'
    }
  );
};

/**
 * 生成刷新令牌
 * @param {Object} payload - JWT载荷数据
 * @returns {string} 刷新令牌
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(
    payload,
    process.env.JWT_REFRESH_SECRET || 'your-default-refresh-secret',
    {
      expiresIn: process.env.JWT_REFRESH_EXPIRE || '30d',
      issuer: 'library-management-system',
      audience: 'library-users'
    }
  );
};

/**
 * 验证访问令牌
 * @param {string} token - JWT令牌
 * @returns {Object} 解码后的载荷数据
 */
const verifyAccessToken = (token) => {
  return jwt.verify(
    token,
    process.env.JWT_SECRET || 'your-default-secret-key',
    {
      issuer: 'library-management-system',
      audience: 'library-users'
    }
  );
};

/**
 * 验证刷新令牌
 * @param {string} token - 刷新令牌
 * @returns {Object} 解码后的载荷数据
 */
const verifyRefreshToken = (token) => {
  return jwt.verify(
    token,
    process.env.JWT_REFRESH_SECRET || 'your-default-refresh-secret',
    {
      issuer: 'library-management-system',
      audience: 'library-users'
    }
  );
};

/**
 * 生成令牌对（访问令牌和刷新令牌）
 * @param {Object} user - 用户对象
 * @returns {Object} 包含访问令牌和刷新令牌的对象
 */
const generateTokenPair = (user) => {
  const payload = {
    userId: user._id,
    email: user.email,
    role: user.role,
    username: user.username
  };

  return {
    accessToken: generateAccessToken(payload),
    refreshToken: generateRefreshToken({ userId: user._id }),
    expiresIn: process.env.JWT_EXPIRE || '7d'
  };
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  generateTokenPair
};
