/**
 * 用户控制器
 * 
 * 处理用户管理相关的请求
 */

const User = require('../models/User');
const BorrowingRecord = require('../models/BorrowingRecord');
const { AppError, asyncHandler } = require('../middlewares/error.middleware');
const { successResponse, errorResponse } = require('../utils/response');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');

/**
 * 获取所有用户（管理员）
 * @route GET /api/users
 * @access Private (Admin only)
 */
const getAllUsers = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    role = 'all',
    status = 'all',
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // 构建查询条件
  const query = {};

  if (search) {
    query.$or = [
      { username: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { 'profile.firstName': { $regex: search, $options: 'i' } },
      { 'profile.lastName': { $regex: search, $options: 'i' } }
    ];
  }

  if (role !== 'all') {
    query.role = role;
  }

  if (status !== 'all') {
    query.isActive = status === 'active';
  }

  // 构建排序条件
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // 分页查询
  const skip = (page - 1) * limit;

  const [users, total] = await Promise.all([
    User.find(query)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit)),
    User.countDocuments(query)
  ]);

  // 获取每个用户的借阅统计
  for (let user of users) {
    const borrowingStats = await BorrowingRecord.aggregate([
      { $match: { user: user._id } },
      {
        $group: {
          _id: null,
          totalBorrowed: { $sum: 1 },
          currentBorrowed: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          overdueFines: { $sum: '$fine' }
        }
      }
    ]);

    user.borrowingStats = borrowingStats[0] || {
      totalBorrowed: 0,
      currentBorrowed: 0,
      overdueFines: 0
    };
  }

  // 分页信息
  const pagination = {
    currentPage: parseInt(page),
    totalPages: Math.ceil(total / limit),
    totalItems: total,
    itemsPerPage: parseInt(limit),
    hasNextPage: page < Math.ceil(total / limit),
    hasPrevPage: page > 1
  };

  res.json(successResponse({
    items: users,
    pagination
  }, '获取用户列表成功'));
});

/**
 * 获取用户详情
 * @route GET /api/users/:id
 * @access Private (Admin or Self)
 */
const getUserById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const currentUser = req.user;

  // 检查权限：管理员或用户本人
  if (currentUser.role !== 'admin' && currentUser.userId !== id) {
    return res.status(403).json(errorResponse('权限不足', 403));
  }

  const user = await User.findById(id).select('-password');
  
  if (!user) {
    return res.status(404).json(errorResponse('用户不存在', 404));
  }

  // 获取用户借阅统计
  const borrowingStats = await BorrowingRecord.aggregate([
    { $match: { user: user._id } },
    {
      $group: {
        _id: null,
        totalBorrowed: { $sum: 1 },
        currentBorrowed: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        overdueFines: { $sum: '$fine' },
        overdueCount: {
          $sum: { $cond: [{ $eq: ['$status', 'overdue'] }, 1, 0] }
        }
      }
    }
  ]);

  user.borrowingStats = borrowingStats[0] || {
    totalBorrowed: 0,
    currentBorrowed: 0,
    overdueFines: 0,
    overdueCount: 0
  };

  res.json(successResponse(user, '获取用户详情成功'));
});

/**
 * 更新用户状态
 * @route PATCH /api/users/:id/status
 * @access Private (Admin only)
 */
const updateUserStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  const user = await User.findByIdAndUpdate(
    id,
    { isActive },
    { new: true }
  ).select('-password');

  if (!user) {
    return res.status(404).json(errorResponse('用户不存在', 404));
  }

  res.json(successResponse(user, '用户状态更新成功'));
});

/**
 * 更新用户角色
 * @route PATCH /api/users/:id/role
 * @access Private (Admin only)
 */
const updateUserRole = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { role } = req.body;

  if (!['admin', 'member'].includes(role)) {
    return res.status(400).json(errorResponse('无效的角色', 400));
  }

  const user = await User.findByIdAndUpdate(
    id,
    { role },
    { new: true }
  ).select('-password');

  if (!user) {
    return res.status(404).json(errorResponse('用户不存在', 404));
  }

  res.json(successResponse(user, '用户角色更新成功'));
});

/**
 * 获取用户统计
 * @route GET /api/users/stats
 * @access Private (Admin only)
 */
const getUserStats = asyncHandler(async (req, res) => {
  const stats = await User.aggregate([
    {
      $group: {
        _id: null,
        totalUsers: { $sum: 1 },
        activeUsers: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        adminUsers: {
          $sum: { $cond: [{ $eq: ['$role', 'admin'] }, 1, 0] }
        },
        memberUsers: {
          $sum: { $cond: [{ $eq: ['$role', 'member'] }, 1, 0] }
        }
      }
    }
  ]);

  const result = stats[0] || {
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    memberUsers: 0
  };

  res.json(successResponse(result, '获取用户统计成功'));
});

/**
 * 更新用户资料
 * @route PUT /api/users/:id/profile
 * @access Private (Admin or Self)
 */
const updateUserProfile = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const currentUser = req.user;
  const updateData = req.body;

  // 检查权限：管理员或用户本人
  if (currentUser.role !== 'admin' && currentUser.userId !== id) {
    return res.status(403).json(errorResponse('权限不足', 403));
  }

  // 移除敏感字段
  delete updateData.password;
  delete updateData.role;
  delete updateData.isActive;

  const user = await User.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  ).select('-password');

  if (!user) {
    return res.status(404).json(errorResponse('用户不存在', 404));
  }

  res.json(successResponse(user, '用户资料更新成功'));
});

/**
 * 修改密码
 * @route PATCH /api/users/:id/password
 * @access Private (Admin or Self)
 */
const changePassword = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const currentUser = req.user;
  const { currentPassword, newPassword } = req.body;

  // 检查权限：管理员或用户本人
  if (currentUser.role !== 'admin' && currentUser.userId !== id) {
    return res.status(403).json(errorResponse('权限不足', 403));
  }

  const user = await User.findById(id);
  if (!user) {
    return res.status(404).json(errorResponse('用户不存在', 404));
  }

  // 如果不是管理员，需要验证当前密码
  if (currentUser.role !== 'admin') {
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json(errorResponse('当前密码错误', 400));
    }
  }

  // 加密新密码
  const saltRounds = 12;
  const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

  await User.findByIdAndUpdate(id, { password: hashedPassword });

  res.json(successResponse(null, '密码修改成功'));
});

/**
 * 获取用户借阅统计
 * @route GET /api/users/:id/borrowing-stats
 * @access Private (Admin or Self)
 */
const getUserBorrowingStats = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const currentUser = req.user;

  // 检查权限：管理员或用户本人
  if (currentUser.role !== 'admin' && currentUser.userId !== id) {
    return res.status(403).json(errorResponse('权限不足', 403));
  }

  const stats = await BorrowingRecord.aggregate([
    { $match: { user: new mongoose.Types.ObjectId(id) } },
    {
      $group: {
        _id: null,
        totalBorrowed: { $sum: 1 },
        currentBorrowed: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        returnedCount: {
          $sum: { $cond: [{ $eq: ['$status', 'returned'] }, 1, 0] }
        },
        overdueCount: {
          $sum: { $cond: [{ $eq: ['$status', 'overdue'] }, 1, 0] }
        },
        totalFines: { $sum: '$fine' }
      }
    }
  ]);

  const result = stats[0] || {
    totalBorrowed: 0,
    currentBorrowed: 0,
    returnedCount: 0,
    overdueCount: 0,
    totalFines: 0
  };

  res.json(successResponse(result, '获取用户借阅统计成功'));
});

module.exports = {
  getAllUsers,
  getUserById,
  updateUserStatus,
  updateUserRole,
  getUserStats,
  updateUserProfile,
  changePassword,
  getUserBorrowingStats
};
