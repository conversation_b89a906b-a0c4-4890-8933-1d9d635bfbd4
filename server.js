/**
 * 图书管理系统后端服务器启动文件
 * 
 * 功能：
 * 1. 加载环境变量
 * 2. 连接数据库
 * 3. 启动Express服务器
 * 4. 错误处理
 */

// 加载环境变量 - 必须在其他模块之前
require('dotenv').config();

const app = require('./src/app');
const connectDB = require('./src/config/database');
const logger = require('./src/utils/logger');

// 获取端口号，默认为3000
const PORT = process.env.PORT || 3000;

/**
 * 启动服务器
 */
async function startServer() {
  try {
    // 连接数据库
    await connectDB();
    logger.info('数据库连接成功');

    // 启动服务器
    const server = app.listen(PORT, () => {
      logger.info(`服务器运行在端口 ${PORT}`);
      logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`API文档: http://localhost:${PORT}/api/docs`);
    });

    // 优雅关闭处理
    process.on('SIGTERM', () => {
      logger.info('收到SIGTERM信号，开始优雅关闭服务器...');
      server.close(() => {
        logger.info('服务器已关闭');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      logger.info('收到SIGINT信号，开始优雅关闭服务器...');
      server.close(() => {
        logger.info('服务器已关闭');
        process.exit(0);
      });
    });

  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

// 未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
startServer();
