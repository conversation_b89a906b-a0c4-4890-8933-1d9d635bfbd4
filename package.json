{"name": "library-management-backend", "version": "1.0.0", "description": "图书管理系统后端API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["library", "management", "nodejs", "express", "mongodb", "rest-api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "winston": "^3.11.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "prettier": "^3.1.1", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}