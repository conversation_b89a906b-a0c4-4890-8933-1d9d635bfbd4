<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/book-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="图书管理系统 - 现代化的图书馆管理解决方案" />
    <meta name="keywords" content="图书管理,图书馆,借阅管理,图书系统" />
    <meta name="author" content="Library Management System" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta property="og:title" content="图书管理系统" />
    <meta property="og:description" content="现代化的图书馆管理解决方案" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="/" />
    <meta property="twitter:title" content="图书管理系统" />
    <meta property="twitter:description" content="现代化的图书馆管理解决方案" />
    <meta property="twitter:image" content="/og-image.png" />

    <!-- PWA相关 -->
    <meta name="theme-color" content="#1890ff" />
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <title>图书管理系统</title>
    
    <!-- 内联关键CSS以提高首屏渲染速度 -->
    <style>
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #f5f5f5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e6f7ff;
        border-top: 4px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 16px;
        color: #666;
        font-size: 14px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      .loading-hidden {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }
      
      /* 防止FOUC */
      #root {
        min-height: 100vh;
      }
      
      /* 基础样式重置 */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
      }
    </style>
  </head>
  <body>
    <!-- 应用根节点 -->
    <div id="root">
      <!-- 初始加载动画 -->
      <div id="initial-loading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载图书管理系统...</div>
      </div>
    </div>
    
    <!-- 主应用脚本 -->
    <script type="module" src="/src/main.jsx"></script>
    
    <!-- 隐藏加载动画的脚本 -->
    <script>
      // 当页面加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingElement = document.getElementById('initial-loading');
          if (loadingElement) {
            loadingElement.classList.add('loading-hidden');
            setTimeout(function() {
              loadingElement.remove();
            }, 300);
          }
        }, 500); // 延迟500ms以确保React应用已经渲染
      });
      
      // 错误处理
      window.addEventListener('error', function(e) {
        console.error('应用加载错误:', e.error);
        const loadingElement = document.getElementById('initial-loading');
        if (loadingElement) {
          const textElement = loadingElement.querySelector('.loading-text');
          if (textElement) {
            textElement.textContent = '加载失败，请刷新页面重试';
            textElement.style.color = '#ff4d4f';
          }
        }
      });
      
      // 检测浏览器兼容性
      (function() {
        const isModernBrowser = (
          'fetch' in window &&
          'Promise' in window &&
          'assign' in Object &&
          'keys' in Object &&
          'addEventListener' in window
        );
        
        if (!isModernBrowser) {
          document.body.innerHTML = `
            <div style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              min-height: 100vh;
              padding: 20px;
              text-align: center;
              font-family: Arial, sans-serif;
            ">
              <h1 style="color: #ff4d4f; margin-bottom: 16px;">浏览器不兼容</h1>
              <p style="color: #666; margin-bottom: 24px;">
                您的浏览器版本过低，无法正常使用本系统。<br>
                请升级到最新版本的 Chrome、Firefox、Safari 或 Edge 浏览器。
              </p>
              <button 
                onclick="window.location.reload()" 
                style="
                  background: #1890ff;
                  color: white;
                  border: none;
                  padding: 8px 16px;
                  border-radius: 4px;
                  cursor: pointer;
                "
              >
                重新加载
              </button>
            </div>
          `;
        }
      })();
    </script>
    
    <!-- 服务工作者注册（PWA支持） -->
    <script>
      if ('serviceWorker' in navigator && location.hostname !== 'localhost') {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
