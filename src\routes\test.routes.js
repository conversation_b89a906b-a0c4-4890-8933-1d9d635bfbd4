/**
 * 测试路由 (Test Routes)
 * 
 * 用于测试认证和授权中间件功能的路由
 * 注意：这些路由仅用于开发和测试，生产环境中应该移除
 */

const express = require('express');
const { protect } = require('../middlewares/auth.middleware');
const { 
  authorize, 
  adminOnly, 
  memberAndAbove,
  canAccessUser,
  requireActiveAccount,
  requireMinRole
} = require('../middlewares/role.middleware');
const { successResponse } = require('../utils/response');

const router = express.Router();

// 公开访问路由
router.get('/public', (req, res) => {
  res.json(successResponse({
    message: '这是一个公开访问的路由',
    timestamp: new Date().toISOString(),
    user: req.user ? req.user.username : '未登录用户'
  }, '公开路由访问成功'));
});

// 需要认证的路由
router.get('/authenticated', protect, (req, res) => {
  res.json(successResponse({
    message: '这是一个需要认证的路由',
    user: {
      username: req.user.username,
      email: req.user.email,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  }, '认证路由访问成功'));
});

// 只允许管理员访问的路由
router.get('/admin-only', protect, adminOnly, (req, res) => {
  res.json(successResponse({
    message: '这是一个只允许管理员访问的路由',
    user: {
      username: req.user.username,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  }, '管理员路由访问成功'));
});

// 允许会员和管理员访问的路由
router.get('/member-and-above', protect, memberAndAbove, (req, res) => {
  res.json(successResponse({
    message: '这是一个允许会员和管理员访问的路由',
    user: {
      username: req.user.username,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  }, '会员路由访问成功'));
});

// 使用authorize中间件的路由（多个角色）
router.get('/multiple-roles', protect, authorize('admin', 'member'), (req, res) => {
  res.json(successResponse({
    message: '这是一个允许多个角色访问的路由',
    allowedRoles: ['admin', 'member'],
    user: {
      username: req.user.username,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  }, '多角色路由访问成功'));
});

// 测试用户资源访问权限
router.get('/user/:userId/profile', protect, canAccessUser('userId'), (req, res) => {
  res.json(successResponse({
    message: '用户资料访问成功',
    targetUserId: req.params.userId,
    currentUser: {
      userId: req.user.userId,
      username: req.user.username,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  }, '用户资料访问成功'));
});

// 测试最低角色要求
router.get('/min-role-member', protect, requireMinRole('member'), (req, res) => {
  res.json(successResponse({
    message: '这是一个需要会员及以上角色的路由',
    minRequiredRole: 'member',
    user: {
      username: req.user.username,
      role: req.user.role
    },
    timestamp: new Date().toISOString()
  }, '最低角色要求路由访问成功'));
});

// 测试账户状态检查
router.get('/active-account-only', protect, requireActiveAccount, (req, res) => {
  res.json(successResponse({
    message: '这是一个只允许激活账户访问的路由',
    user: {
      username: req.user.username,
      role: req.user.role,
      isActive: req.user.isActive
    },
    timestamp: new Date().toISOString()
  }, '激活账户路由访问成功'));
});

// 组合中间件测试
router.get('/complex-auth', 
  protect, 
  requireActiveAccount, 
  authorize('admin', 'member'), 
  (req, res) => {
    res.json(successResponse({
      message: '这是一个使用复杂认证和授权的路由',
      requirements: [
        '需要认证',
        '需要激活账户',
        '需要admin或member角色'
      ],
      user: {
        username: req.user.username,
        role: req.user.role,
        isActive: req.user.isActive
      },
      timestamp: new Date().toISOString()
    }, '复杂认证路由访问成功'));
  }
);

// 错误测试路由 - 故意触发错误
router.get('/trigger-error', protect, (req, res, next) => {
  // 故意抛出错误来测试错误处理
  const error = new Error('这是一个测试错误');
  error.statusCode = 500;
  next(error);
});

// 中间件功能测试信息路由
router.get('/middleware-info', (req, res) => {
  res.json(successResponse({
    availableTestRoutes: [
      {
        path: '/api/test/public',
        method: 'GET',
        description: '公开访问路由',
        requirements: '无'
      },
      {
        path: '/api/test/authenticated',
        method: 'GET',
        description: '需要认证的路由',
        requirements: '有效的JWT令牌'
      },
      {
        path: '/api/test/admin-only',
        method: 'GET',
        description: '管理员专用路由',
        requirements: '认证 + admin角色'
      },
      {
        path: '/api/test/member-and-above',
        method: 'GET',
        description: '会员及以上路由',
        requirements: '认证 + member或admin角色'
      },
      {
        path: '/api/test/multiple-roles',
        method: 'GET',
        description: '多角色访问路由',
        requirements: '认证 + admin或member角色'
      },
      {
        path: '/api/test/user/:userId/profile',
        method: 'GET',
        description: '用户资料访问路由',
        requirements: '认证 + 访问自己的资料或admin角色'
      },
      {
        path: '/api/test/min-role-member',
        method: 'GET',
        description: '最低角色要求路由',
        requirements: '认证 + member及以上角色'
      },
      {
        path: '/api/test/active-account-only',
        method: 'GET',
        description: '激活账户专用路由',
        requirements: '认证 + 激活的账户'
      },
      {
        path: '/api/test/complex-auth',
        method: 'GET',
        description: '复杂认证路由',
        requirements: '认证 + 激活账户 + admin或member角色'
      }
    ],
    usage: {
      authentication: 'Authorization: Bearer <your-jwt-token>',
      testUsers: {
        admin: '<EMAIL> / Admin123456',
        member: '<EMAIL> / Member123456'
      }
    }
  }, '中间件测试路由信息'));
});

module.exports = router;
