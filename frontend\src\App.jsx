/**
 * 根组件
 * 
 * 应用程序的主要组件，包含路由配置和全局布局
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// 布局组件
import MainLayout from './components/common/Layout/MainLayout';
import AuthLayout from './components/common/Layout/AuthLayout';

// 页面组件
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import BooksListPage from './pages/books/BooksListPage';
import BookDetailPage from './pages/books/BookDetailPage';
import MyBorrowingsPage from './pages/borrowing/MyBorrowingsPage';
import BorrowingHistoryPage from './pages/borrowing/BorrowingHistoryPage';
import ProfilePage from './pages/profile/ProfilePage';
import AdminDashboard from './pages/admin/AdminDashboard';
import BookManagePage from './pages/books/BookManagePage';
import UserManagePage from './pages/admin/UserManagePage';
import StatisticsPage from './pages/admin/StatisticsPage';
import NotFoundPage from './pages/NotFoundPage';

// 路由保护组件
import ProtectedRoute from './components/common/ProtectedRoute';
import AdminRoute from './components/common/AdminRoute';

// 错误边界
import ErrorBoundary from './components/common/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <div className="App">
        <Routes>
          {/* 认证相关路由 */}
          <Route path="/auth" element={<AuthLayout />}>
            <Route path="login" element={<LoginPage />} />
            <Route path="register" element={<RegisterPage />} />
            <Route index element={<Navigate to="login" replace />} />
          </Route>

          {/* 主应用路由 */}
          <Route path="/" element={<ProtectedRoute><MainLayout /></ProtectedRoute>}>
            {/* 默认重定向到图书列表 */}
            <Route index element={<Navigate to="/books" replace />} />
            
            {/* 图书相关路由 */}
            <Route path="books">
              <Route index element={<BooksListPage />} />
              <Route path=":id" element={<BookDetailPage />} />
            </Route>

            {/* 借阅相关路由 */}
            <Route path="borrowings">
              <Route index element={<MyBorrowingsPage />} />
              <Route path="history" element={<BorrowingHistoryPage />} />
            </Route>

            {/* 个人中心 */}
            <Route path="profile" element={<ProfilePage />} />

            {/* 管理员路由 */}
            <Route path="admin" element={<AdminRoute />}>
              <Route index element={<AdminDashboard />} />
              <Route path="books" element={<BookManagePage />} />
              <Route path="users" element={<UserManagePage />} />
              <Route path="statistics" element={<StatisticsPage />} />
            </Route>
          </Route>

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        {/* 全局通知组件 */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#fff',
              color: '#333',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            },
            success: {
              iconTheme: {
                primary: '#52c41a',
                secondary: '#fff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ff4d4f',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </ErrorBoundary>
  );
}

export default App;
