/**
 * 管理员面板页面
 * 
 * 系统概览和统计信息
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Table,
  Tag,
  Button,
  Space,
  Progress,
  List,
  Avatar,
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  ReadOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { bookService } from '../../services/bookService';
import { borrowingService } from '../../services/borrowingService';
import { BORROWING_STATUS_LABELS } from '../../utils/constants';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

function AdminDashboard() {
  const navigate = useNavigate();

  // 获取图书统计
  const { data: bookStats } = useQuery(
    ['bookStats'],
    () => bookService.getBookStats(),
    {
      staleTime: 5 * 60 * 1000, // 5分钟
    }
  );

  // 获取逾期记录
  const { data: overdueRecords } = useQuery(
    ['overdueRecords'],
    () => borrowingService.getOverdueRecords(),
    {
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );

  // 获取最新借阅记录
  const { data: recentBorrowings } = useQuery(
    ['recentBorrowings'],
    () => borrowingService.getAllBorrowingRecords({ limit: 10, sortBy: 'borrowDate', sortOrder: 'desc' }),
    {
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );

  // 获取热门图书
  const { data: popularBooks } = useQuery(
    ['popularBooks'],
    () => bookService.getPopularBooks(5),
    {
      staleTime: 10 * 60 * 1000, // 10分钟
    }
  );

  // 逾期记录表格列
  const overdueColumns = [
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user) => (
        <div>
          <div className="font-medium">{user.username}</div>
          <div className="text-gray-500 text-sm">{user.email}</div>
        </div>
      ),
    },
    {
      title: '图书',
      dataIndex: 'book',
      key: 'book',
      render: (book) => (
        <div>
          <div className="font-medium">{book.title}</div>
          <div className="text-gray-500 text-sm">{book.author}</div>
        </div>
      ),
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date) => {
        const overdueDays = dayjs().diff(dayjs(date), 'day');
        return (
          <div>
            <div>{dayjs(date).format('YYYY-MM-DD')}</div>
            <Tag color="error" size="small">
              逾期 {overdueDays} 天
            </Tag>
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/admin/borrowings/${record._id}`)}
        >
          查看
        </Button>
      ),
    },
  ];

  // 计算统计数据
  const stats = bookStats?.data || {};
  const overview = stats.overview || {};
  const categoryStats = stats.categoryStats || [];

  // 计算借阅率
  const borrowingRate = overview.totalCopies > 0 
    ? ((overview.totalCopies - overview.totalStock) / overview.totalCopies * 100).toFixed(1)
    : 0;

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-6">
        <Title level={2}>管理面板</Title>
        <Text className="text-gray-600">
          系统概览和统计信息
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="图书总数"
              value={overview.totalBooks || 0}
              prefix={<BookOutlined />}
              suffix="本"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="可借图书"
              value={overview.availableBooks || 0}
              prefix={<BookOutlined />}
              suffix="本"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已借图书"
              value={overview.borrowedBooks || 0}
              prefix={<ReadOutlined />}
              suffix="本"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="逾期记录"
              value={overdueRecords?.data?.items?.length || 0}
              prefix={<ExclamationCircleOutlined />}
              suffix="条"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 左侧列 */}
        <Col xs={24} lg={16}>
          {/* 借阅率统计 */}
          <Card title="借阅率统计" className="mb-4">
            <Row gutter={16}>
              <Col span={12}>
                <div className="text-center">
                  <Progress
                    type="circle"
                    percent={parseFloat(borrowingRate)}
                    format={(percent) => `${percent}%`}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                  <div className="mt-2">
                    <Text className="text-gray-600">总体借阅率</Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <Space direction="vertical" className="w-full">
                  <div className="flex justify-between">
                    <Text>总册数</Text>
                    <Text strong>{overview.totalCopies || 0}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>在库数</Text>
                    <Text strong>{overview.totalStock || 0}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>借出数</Text>
                    <Text strong>{(overview.totalCopies || 0) - (overview.totalStock || 0)}</Text>
                  </div>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 逾期记录 */}
          <Card
            title="逾期记录"
            extra={
              <Button
                type="primary"
                onClick={() => navigate('/admin/borrowings?status=overdue')}
              >
                查看全部
              </Button>
            }
          >
            <Table
              columns={overdueColumns}
              dataSource={overdueRecords?.data?.items?.slice(0, 5) || []}
              pagination={false}
              size="small"
              locale={{ emptyText: '暂无逾期记录' }}
            />
          </Card>
        </Col>

        {/* 右侧列 */}
        <Col xs={24} lg={8}>
          {/* 分类统计 */}
          <Card title="分类统计" className="mb-4">
            <List
              size="small"
              dataSource={categoryStats.slice(0, 8)}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    title={item._id}
                    description={`${item.count} 本图书`}
                  />
                  <div className="text-right">
                    <div className="text-green-600">{item.availableCount} 可借</div>
                    <div className="text-gray-500 text-sm">{item.totalStock} 在库</div>
                  </div>
                </List.Item>
              )}
              locale={{ emptyText: '暂无数据' }}
            />
          </Card>

          {/* 热门图书 */}
          <Card title="热门图书">
            <List
              size="small"
              dataSource={popularBooks?.data?.items?.slice(0, 5) || []}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        size="small"
                        style={{
                          backgroundColor: index < 3 ? '#f56a00' : '#87d068',
                        }}
                      >
                        {index + 1}
                      </Avatar>
                    }
                    title={
                      <div className="text-ellipsis" title={item.title}>
                        {item.title}
                      </div>
                    }
                    description={
                      <div>
                        <div className="text-ellipsis">{item.author}</div>
                        <div className="text-xs text-gray-500">
                          借阅 {item.borrowingStats?.totalBorrowed || 0} 次
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无数据' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 最新借阅记录 */}
      <Card
        title="最新借阅记录"
        className="mt-4"
        extra={
          <Button
            type="primary"
            onClick={() => navigate('/admin/borrowings')}
          >
            查看全部
          </Button>
        }
      >
        <Table
          columns={[
            {
              title: '用户',
              dataIndex: 'user',
              key: 'user',
              render: (user) => user?.username || '-',
            },
            {
              title: '图书',
              dataIndex: 'book',
              key: 'book',
              render: (book) => book?.title || '-',
            },
            {
              title: '借阅时间',
              dataIndex: 'borrowDate',
              key: 'borrowDate',
              render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm'),
            },
            {
              title: '应还时间',
              dataIndex: 'dueDate',
              key: 'dueDate',
              render: (date) => dayjs(date).format('YYYY-MM-DD'),
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              render: (status) => {
                const label = BORROWING_STATUS_LABELS[status];
                return <Tag color={label?.color}>{label?.text}</Tag>;
              },
            },
          ]}
          dataSource={recentBorrowings?.data?.items || []}
          pagination={false}
          size="small"
          locale={{ emptyText: '暂无借阅记录' }}
        />
      </Card>
    </div>
  );
}

export default AdminDashboard;
