# 图书管理系统前端

基于React + Vite + Ant Design的图书管理系统前端应用。

## 技术栈

- **框架**: React 18
- **构建工具**: Vite
- **UI库**: Ant Design 5
- **路由**: React Router DOM 6
- **HTTP客户端**: Axios
- **状态管理**: Zustand + React Query
- **表单处理**: React Hook Form + Yup
- **样式**: Tailwind CSS + Ant Design
- **图标**: Ant Design Icons
- **日期处理**: Day.js
- **工具库**: Lodash
- **通知**: React Hot Toast

## 项目结构

```
frontend/
├── public/                     # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── components/             # 可复用组件
│   │   ├── common/             # 通用组件
│   │   │   ├── Layout/         # 布局组件
│   │   │   ├── Loading/        # 加载组件
│   │   │   ├── ErrorBoundary/  # 错误边界
│   │   │   └── ProtectedRoute/ # 路由保护
│   │   ├── forms/              # 表单组件
│   │   │   ├── BookForm/       # 图书表单
│   │   │   └── UserForm/       # 用户表单
│   │   └── ui/                 # UI组件
│   │       ├── BookCard/       # 图书卡片
│   │       ├── BookTable/      # 图书表格
│   │       └── SearchBar/      # 搜索栏
│   ├── pages/                  # 页面组件
│   │   ├── auth/               # 认证页面
│   │   │   ├── LoginPage.jsx
│   │   │   └── RegisterPage.jsx
│   │   ├── books/              # 图书页面
│   │   │   ├── BooksListPage.jsx
│   │   │   ├── BookDetailPage.jsx
│   │   │   └── BookManagePage.jsx
│   │   ├── borrowing/          # 借阅页面
│   │   │   ├── MyBorrowingsPage.jsx
│   │   │   └── BorrowingHistoryPage.jsx
│   │   ├── admin/              # 管理员页面
│   │   │   ├── AdminDashboard.jsx
│   │   │   ├── UserManagePage.jsx
│   │   │   └── StatisticsPage.jsx
│   │   ├── profile/            # 个人中心
│   │   │   └── ProfilePage.jsx
│   │   └── NotFoundPage.jsx    # 404页面
│   ├── services/               # API服务
│   │   ├── api.js              # Axios配置
│   │   ├── authService.js      # 认证服务
│   │   ├── bookService.js      # 图书服务
│   │   ├── borrowingService.js # 借阅服务
│   │   └── userService.js      # 用户服务
│   ├── hooks/                  # 自定义Hooks
│   │   ├── useAuth.js          # 认证Hook
│   │   ├── useBooks.js         # 图书Hook
│   │   ├── useBorrowing.js     # 借阅Hook
│   │   └── useLocalStorage.js  # 本地存储Hook
│   ├── context/                # React Context
│   │   ├── AuthContext.jsx     # 认证上下文
│   │   └── ThemeContext.jsx    # 主题上下文
│   ├── utils/                  # 工具函数
│   │   ├── constants.js        # 常量定义
│   │   ├── helpers.js          # 辅助函数
│   │   ├── validators.js       # 验证函数
│   │   └── formatters.js       # 格式化函数
│   ├── styles/                 # 样式文件
│   │   ├── globals.css         # 全局样式
│   │   ├── components.css      # 组件样式
│   │   └── antd-overrides.css  # Ant Design样式覆盖
│   ├── assets/                 # 静态资源
│   │   ├── images/             # 图片
│   │   └── icons/              # 图标
│   ├── test/                   # 测试文件
│   │   ├── setup.js            # 测试配置
│   │   └── __mocks__/          # Mock文件
│   ├── App.jsx                 # 根组件
│   ├── main.jsx                # 入口文件
│   └── router.jsx              # 路由配置
├── .env.example                # 环境变量示例
├── .gitignore                  # Git忽略文件
├── package.json                # 项目配置
├── vite.config.js              # Vite配置
├── tailwind.config.js          # Tailwind配置
├── postcss.config.js           # PostCSS配置
└── README.md                   # 项目说明
```

## 核心依赖说明

### 生产依赖

- **react** & **react-dom**: React核心库
- **react-router-dom**: 客户端路由管理
- **axios**: HTTP请求库，用于API调用
- **antd**: Ant Design UI组件库
- **@ant-design/icons**: Ant Design图标库
- **dayjs**: 轻量级日期处理库
- **react-query**: 服务器状态管理和缓存
- **zustand**: 轻量级状态管理库
- **react-hook-form**: 高性能表单库
- **@hookform/resolvers**: 表单验证解析器
- **yup**: 数据验证库
- **react-hot-toast**: 通知组件
- **lodash**: JavaScript工具库

### 开发依赖

- **@vitejs/plugin-react**: Vite React插件
- **vite**: 构建工具
- **eslint**: 代码检查工具
- **vitest**: 测试框架
- **@testing-library/react**: React测试工具
- **tailwindcss**: CSS框架
- **autoprefixer**: CSS自动前缀
- **postcss**: CSS处理工具

## 快速开始

### 1. 安装依赖

```bash
cd frontend
npm install
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
nano .env
```

### 3. 启动开发服务器

```bash
npm run dev
```

### 4. 构建生产版本

```bash
npm run build
```

### 5. 预览生产版本

```bash
npm run preview
```

## 开发指南

### 组件开发规范

1. **组件命名**: 使用PascalCase命名
2. **文件结构**: 每个组件一个文件夹，包含index.jsx和样式文件
3. **Props验证**: 使用PropTypes或TypeScript进行类型检查
4. **样式管理**: 优先使用Ant Design组件，必要时使用Tailwind CSS

### API调用规范

1. **服务层**: 所有API调用都通过services层进行
2. **错误处理**: 统一在Axios拦截器中处理
3. **加载状态**: 使用React Query管理加载和错误状态
4. **缓存策略**: 合理设置React Query的缓存时间

### 状态管理规范

1. **本地状态**: 使用useState和useReducer
2. **全局状态**: 使用Zustand管理应用级状态
3. **服务器状态**: 使用React Query管理API数据
4. **表单状态**: 使用React Hook Form管理表单

### 路由管理

1. **路由保护**: 使用ProtectedRoute组件保护需要认证的路由
2. **懒加载**: 大型页面组件使用React.lazy进行代码分割
3. **路由参数**: 合理使用URL参数传递状态

## 部署

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
VITE_API_URL=https://your-api-domain.com/api
VITE_APP_NAME=图书管理系统
VITE_ENABLE_DEV_TOOLS=false
```

### 构建和部署

```bash
# 构建
npm run build

# 部署到静态文件服务器
# 将dist目录的内容上传到服务器
```

## 测试

```bash
# 运行测试
npm run test

# 运行测试UI
npm run test:ui
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
