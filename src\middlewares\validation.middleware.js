/**
 * 数据验证中间件
 * 
 * 使用express-validator进行请求数据验证
 */

const { validationResult } = require('express-validator');
const { validationErrorResponse } = require('../utils/response');

/**
 * 验证请求数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    return res.status(400).json(validationErrorResponse(errorDetails));
  }
  
  next();
};

module.exports = {
  validate
};
