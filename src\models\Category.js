/**
 * 图书分类模型 (Category Model)
 * 
 * 定义图书分类数据结构和相关方法
 */

const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '分类名称是必需的'],
    unique: true,
    trim: true,
    maxlength: [50, '分类名称不能超过50个字符']
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [200, '分类描述不能超过200个字符']
  },
  
  parentCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  // 分类统计
  stats: {
    bookCount: {
      type: Number,
      default: 0
    },
    borrowingCount: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段：子分类
categorySchema.virtual('subcategories', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parentCategory'
});

// 索引（name的unique索引已在字段定义中设置）
categorySchema.index({ parentCategory: 1 });
categorySchema.index({ isActive: 1 });
categorySchema.index({ 'stats.bookCount': -1 });

// 静态方法：获取分类树
categorySchema.statics.getCategoryTree = function() {
  return this.find({ isActive: true })
    .populate('subcategories')
    .sort({ name: 1 });
};

const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
